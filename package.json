{"name": "eisqr", "version": "1.0.0", "homepage": "/", "private": false, "license": "MIT", "dependencies": {"@cyntler/react-doc-viewer": "^1.17.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@formkit/auto-animate": "^0.8.2", "@fullcalendar/core": "^5.7.2", "@fullcalendar/daygrid": "^5.7.2", "@fullcalendar/interaction": "^5.7.2", "@fullcalendar/react": "^5.7.0", "@fullcalendar/timegrid": "^5.7.2", "@material-ui/core": "^4.12.4", "@mui/icons-material": "^6.2.1", "@mui/lab": "^6.0.0-beta.14", "@mui/material": "^6.1.6", "@mui/styles": "^6.1.6", "@react-google-maps/api": "^2.12.2", "@reduxjs/toolkit": "^1.8.6", "amazon-cognito-identity-js": "^6.3.12", "axios": "^0.19.0", "bootstrap": "^5.3.2", "chart.js": "^3.7.1", "chartjs-plugin-trendline": "^2.0.2", "classnames": "^2.2.6", "cogo-toast": "^4.2.3", "cropperjs": "^1.6.2", "crypto-js": "^4.2.0", "d3": "^7.9.0", "d3-sankey": "^0.12.3", "d3-scale-chromatic": "^3.1.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "formBuilder": "^3.8.3", "formik": "^2.2.9", "framer-motion": "^4.1.17", "html2canvas": "^1.4.1", "jquery": "^3.6.1", "jquery-ui-sortable": "^1.0.0", "jspdf": "^2.5.2", "leaflet": "^1.9.4", "leaflet.heat": "^0.2.0", "luxon": "^3.3.0", "moment": "^2.29.4", "ngrok": "^4.3.3", "pdfmake": "^0.2.7", "primeflex": "3.1.0", "primeicons": "^5.0.0", "primereact": "^9.6.4", "prismjs": "1.9.0", "quill": "^1.3.7", "rc-tree": "^5.12.0", "react": "^18.3.1", "react-app-polyfill": "^1.0.6", "react-beautiful-dnd": "^13.1.1", "react-bigpicture": "^1.0.26", "react-bootstrap": "^2.9.1", "react-bootstrap-editable": "^0.8.2", "react-checkbox-tree": "^1.8.0", "react-datepicker": "^4.23.0", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-edit-text": "^5.0.2", "react-editext": "^5.1.0", "react-gauge-chart": "^0.5.1", "react-icons": "^4.6.0", "react-image-crop": "^10.0.8", "react-leaflet": "^5.0.0", "react-quill": "^2.0.0", "react-redux": "^8.0.4", "react-router-dom": "^5.2.0", "react-scripts": "^4.0.3", "react-select": "^5.8.0", "react-sticky": "^6.0.3", "react-tiny-popover": "^7.2.0", "react-to-print": "^3.0.5", "react-tooltip": "^4.5.1", "react-transition-group": "^4.4.1", "read-excel-file": "^5.5.0", "recharts": "^2.12.7", "redux": "^4.2.0", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "sass": "^1.57.1", "sweetalert2": "^11.15.10", "uninstall": "^0.0.0", "use-force-update": "^1.0.10", "xlsx": "^0.18.5", "xlsx-populate": "^1.21.0", "yup": "^0.32.11"}, "scripts": {"start": "react-scripts --openssl-legacy-provider start", "build": "react-scripts --openssl-legacy-provider build", "test": "jest", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "overrides": {"@react-pdf/font": "2.2.1", "@react-pdf/pdfkit": "2.1.0"}}