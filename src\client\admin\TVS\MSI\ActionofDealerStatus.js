import React, { useMemo } from 'react';
import { Accordion, AccordionTab } from 'primereact/accordion';
import Typography from '@mui/material/Typography';
import moment from 'moment';
import { useSelector } from 'react-redux';
import { Timeline } from 'primereact/timeline';
import { Card } from 'primereact/card';
import { Tag } from 'primereact/tag';
import { API } from '../../../../constants/api_url';

const customFontStyle = {
  fontFamily: 'Lato, sans-serif',
};

function ActionofDealerStatus({ report }) {
  const userList = useSelector((state) => state.userlist.userList);

  const getName = (id) => {
    const user = userList.find(user => String(user.id) === String(id));
    return user?.firstName || user?.email || '';
  };

  const groupedActions = useMemo(() => {
    if (!Array.isArray(report.actions)) return [];

    const actionsByTrackId = {};
    report.actions.forEach(action => {
      const trackId = action.trackId || 'untracked';
      if (!actionsByTrackId[trackId]) {
        actionsByTrackId[trackId] = [];
      }
      actionsByTrackId[trackId].push(action);
    });

    Object.keys(actionsByTrackId).forEach(trackId => {
      actionsByTrackId[trackId].sort((a, b) => (a.id || 0) - (b.id || 0));
    });

    return Object.entries(actionsByTrackId).map(([trackId, actions]) => {
      const lastAction = actions[actions.length - 1];
      const isCompleted = lastAction &&
        lastAction.actionType === "Checklist Submission Review" &&
        lastAction.status === "Completed";

      return {
        trackId,
        actions,
        isCompleted,
        primaryAction: actions[0]
      };
    });
  }, [report.actions]);

  const getStatusClass = (status) => {
    const statusClass = {
      Initiated: 'status-tag-blue',
      initiated: 'status-tag-blue',
      Submitted: 'status-tag-orange',
      submitted: 'status-tag-orange',
      Completed: 'status-tag-green',
      completed: 'status-tag-green'
    };
    return statusClass[status] || 'status-tag-blue';
  };

  const getGroupHeader = (group, index) => {
    const action = group.primaryAction;

    return (
      <Typography variant="body1" style={customFontStyle}>
        <div className='row d-flex mb-2'>
          <div className='col-6'>
            <h5>{`Action Group ${index + 1}`}</h5>
            <p className='p-0' style={{ fontSize: '14px' }}>{action.description}</p>
          </div>
          <div className='col-6 text-end'>
            <Tag
              className={`fw-bold ${group.isCompleted ? 'status-tag-green' : 'status-tag-orange'}`}
              value={group.isCompleted ? 'Completed' : 'In Progress'}
            />
          </div>
        </div>
        <div className='row d-flex'>
          <div className='col-6'>
            <strong>Due Date:</strong> {action.dueDate ? moment(action.dueDate).format('Do MMM YYYY') : 'Not set'}
          </div>
          <div className='col-6 text-end'>
            <strong>Actions:</strong> {group.actions.length}
          </div>
        </div>
      </Typography>
    );
  };

  const getActionTimelineItemsWithVersioning = (actions) => {
    if (!actions || !Array.isArray(actions)) return [];

    const sortedActions = [...actions].sort((a, b) => (a.id || 0) - (b.id || 0));

    let version = 1;
    const timeline = [];

    for (let i = 0; i < sortedActions.length; i++) {
      const action = sortedActions[i];
      const createdDate = moment(action.created_on || action.createdDate).format('MMM DD, YYYY [at] h:mm A');
      const submittedBy = getName(action.submittedBy);
      let label = '';
      let color = '#3B82F6';
      let icon = 'pi pi-check-circle';

      if (action.actionType === "Checklist Submission" && action.status === "Completed") {
        label = `A${version}.0: Action Taken`;
        color = '#F59E0B';
      } else if (action.actionType === "Checklist Submission Returned" && action.status === "Completed") {
        version += 1;
        label = `A${version}.0: Re-Action Taken`;
        color = '#F97316';
      } else if (action.actionType === "Checklist Submission Review" && action.status === "Completed") {
        const hasReturnLater = sortedActions.slice(i + 1).some(
          (a) => a.actionType === "Checklist Submission Returned"
        );

        if (hasReturnLater) {
          label = `Reviewed by ${submittedBy} – Returned with comments: ${action.comments || '—'}`;
          color = '#EF4444';
        } else {
          label = `Verified and Closed by ${submittedBy}`;
          color = '#22C55E';
        }
      } else if (action.actionType === "Checklist Submission Review" && action.status === "Initiated") {
        label = `Under Review by ${submittedBy}`;
        color = '#3B82F6';
      } else {
        label = `${action.actionType} - ${action.status}`;
      }

      timeline.push({
        version: `A${version}.0`,
        status: label,
        date: createdDate,
        icon,
        color,
        content: action
      });
    }

    return timeline;
  };

  const getGroupContent = (group) => {
    const timelineEvents = getActionTimelineItemsWithVersioning(group.actions);

    return (
      <div className="obs-section p-4">
        <Card className="mb-4 shadow-sm">
          <div className="row mb-3">
            <div className="col-md-6">
              <p className="obs-title">Action Type</p>
              <p className="obs-content">{group.primaryAction.actionType || 'Not specified'}</p>
            </div>
            <div className="col-md-6">
              <p className="obs-title">Category</p>
              <p className="obs-content">
                {group.primaryAction.categoryOfFinding === 1 ? 'Good Practices' :
                  group.primaryAction.categoryOfFinding === 2 ? 'Opportunity of Improvement' :
                    group.primaryAction.categoryOfFinding === 3 ? 'Non-compliance' : 'Not specified'}
              </p>
            </div>
          </div>
        </Card>

        <h5 className="mb-3">Action History</h5>
        <Timeline
          value={timelineEvents}
          content={(item) => renderTimelineContent(item.content)}
          opposite={(item) => (
            <div>
              <span className="font-bold">{item.status}</span>
              <div className="text-sm text-gray-500">{item.date}</div>
            </div>
          )}
          marker={(item) => (
            <i className={item.icon} style={{ color: item.color, fontSize: '1.5rem' }}></i>
          )}
        />
      </div>
    );
  };

  const renderTimelineContent = (action) => {
    console.log(action, ' ac hitn')
    const isReturnedReview = action.actionType === "Checklist Submission Review"
      && action.status === "Completed";

    const isReviewReturned = isReturnedReview && action.comments;

    if (isReviewReturned) {
      return (
        <Card className="mb-3 shadow-sm">
          <div className="row mb-3">
            <div className="col-md-6">
              <p className="obs-title">Returned By</p>
              <p className="obs-content">{getName(action.submittedBy)}</p>
            </div>
            <div className="col-md-6">
              <p className="obs-title">Comments</p>
              <p className="obs-content">{action.comments || '—'}</p>
            </div>
          </div>
        </Card>
      );
    }

    // Default full content for other cases
    return (
      <Card className="mb-3 shadow-sm">
        <div className="row mb-3">
          <div className="col-md-6">
            <p className="obs-title">Description</p>
            <p className="obs-content">{action.description}</p>
          </div>
          <div className="col-md-6">
            <p className="obs-title">Status</p>
            <p className="obs-content">
              <Tag
                className={getStatusClass(action.status)}
                value={action.status}
              />
            </p>
          </div>
        </div>

        <div className="row mb-3">
          <div className="col-md-6">
            <p className="obs-title">Assigned Action</p>
            <p className="obs-content">{action.actionToBeTaken}</p>
          </div>
          <div className="col-md-6">
            <p className="obs-title">Remarks</p>
            <p className="obs-content">{action.remarks || '—'}</p>
          </div>
        </div>

        {action.actionTaken && (
          <div className="row mb-3">
            <div className="col-md-6">
              <p className="obs-title">Action Taken</p>
              <p className="obs-content">{action.actionTaken}</p>
            </div>
            <div className="col-md-6">
              <p className="obs-title">Taken By</p>
              <p className="obs-content">{getName(action.submittedBy)}</p>
            </div>
          </div>
        )}

        {Array.isArray(action.uploads) && action.uploads.length > 0 && (


          <div className="row">
            {console.log(action.uploads, ' uploads')}
            <div className="col-12">
              <p className="obs-title">Uploads</p>
              <div className="row">
                {action.uploads.map((file, i) => {
                  const fileExt = file.split('.').pop().toLowerCase();

                  if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
                    return (
                      <div className="col-md-3 mb-2" key={i}>
                        <a href={API.Docs + file} target="_blank" rel="noopener noreferrer">
                          <img
                            src={API.Docs + file}
                            alt={`upload-${i}`}
                            className="img-fluid rounded shadow-sm"
                            style={{ maxHeight: '150px', objectFit: 'cover' }}
                          />
                        </a>
                      </div>
                    );
                  } else if (fileExt === 'pdf') {
                    return (
                      <div className="col-md-3 mb-2" key={i}>
                        <a href={API.Docs + file} target="_blank" rel="noopener noreferrer">📄 View PDF</a>
                      </div>
                    );
                  } else {
                    return (
                      <div className="col-md-3 mb-2" key={i}>
                        <a href={API.Docs + file} target="_blank" rel="noopener noreferrer">{file}</a>
                      </div>
                    );
                  }
                })}
              </div>
            </div>
          </div>
        )}
      </Card>
    );
  };

  return (
    <div>
      <h3 className="mb-4">Action Status Report</h3>
      {groupedActions.length === 0 ? (
        <div className="p-4 text-center">
          <h5>No actions found</h5>
        </div>
      ) : (
        <Accordion>
          {groupedActions.map((group, index) => (
            <AccordionTab
              key={group.trackId}
              header={getGroupHeader(group, index)}
              headerClassName="test-header"
            >
              {getGroupContent(group)}
            </AccordionTab>
          ))}
        </Accordion>
      )}
    </div>
  );
}

export default ActionofDealerStatus;