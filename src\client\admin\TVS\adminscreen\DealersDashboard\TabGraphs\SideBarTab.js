import React, { useState, useRef, useEffect } from "react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  LabelList,
  ResponsiveContainer,
} from "recharts";
import { Card } from "primereact/card";
import { But<PERSON> } from "primereact/button";
import { Menu } from "primereact/menu";

// Example datasets for tab 2 and tab 3
const barChartDataTab3 = [
  { region: "West 2", totalDealers: 2, audited: 0 },
  { region: "West 1", totalDealers: 3, audited: 0 },
  { region: "South 2", totalDealers: 3, audited: 1 },
  { region: "South 1", totalDealers: 2, audited: 1 },
  { region: "North", totalDealers: 2, audited:0 },
  { region: "East", totalDealers: 5, audited:2},
  { region: "Central", totalDealers: 2, audited: 0 },
].map(({ totalDealers, audited, ...rest }) => ({ ...rest, totalDealers, audited, auditedPercent: ((audited / totalDealers) * 100).toFixed(2) }))

const barChartDataTab2 = [
  // { region: "West 2", totalDealers: 0, audited: 0 },
  { region: "West ", totalDealers: 15, audited: 0 },
  { region: "South 2", totalDealers: 19, audited: 0 },
  { region: "South 1", totalDealers: 8, audited: 2 },
  { region: "North", totalDealers: 19, audited: 1 },
  { region: "East", totalDealers: 20, audited: 1 },
  // { region: "Central", totalDealers: 0, audited: 0 },
].map(({ totalDealers, audited, ...rest }) => ({ ...rest, totalDealers, audited, auditedPercent: ((audited / totalDealers) * 100).toFixed(2) }))

const SideBarTab = ({ tabIndex }) => {
  const [chartData, setChartData] = useState([]);
  const [activeMode, setActiveMode] = useState(true);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const menuRef = useRef(null);

  useEffect(() => {
    if (tabIndex === 1) {
      setChartData([...barChartDataTab2]);
    } else if (tabIndex === 2) {
      setChartData([...barChartDataTab3]);
    }
  }, [tabIndex]);

  const panelItems = [
    {
      items: [
        {
          label: "Export as Excel",
          icon: "pi pi-file-excel",
          command: () => {
            // downloadExcelWithImage(chartRef);
          },
        },
        {
          label: "Export as PDF",
          icon: "pi pi-file-pdf",
          command: () => {
            // downloadPdfWithImage(chartRef);
          },
        },
        {
          label: "Export as JPG",
          icon: "pi pi-image",
          command: () => {
            // downloadChartAsJpg(chartRef);
          },
        },
        activeMode && {
          label: "Print",
          icon: "pi pi-print",
          command: () => {
            // printChart(chartRef);
          },
        },
      ],
    },
  ];

  const CustomLegend = (props) => {
    const { payload } = props;
    return (
      <ul
        style={{
          display: "flex",
          listStyleType: "none",
          justifyContent: "center",
          padding: 0,
        }}
      >
        {payload.map((entry, index) => (
          <li
            key={`item-${index}`}
            style={{
              color: entry.color,
              marginRight: "5px",
            }}
          >
            <span
              style={{
                color: entry.color,
                backgroundColor: entry.color,
                marginRight: 4,
                fontSize: "20px",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                display: "inline-block",
              }}
            ></span>
            <span style={{ color: "#555", fontSize: "14px" }}>
              {entry.value}
            </span>
          </li>
        ))}
      </ul>
    );
  };

  return (
    <Card style={{ width: "100%" }}>
      <div style={{ display: "flex", justifyContent: "space-between" }}>
        <h3 style={{ fontSize: "18px", margin: "25px" }}>
          Total No of {tabIndex === 1 ? "APS" : "Area Office"} vs{" "}
          {tabIndex === 1 ? "APS" : "Area Office"} Calibrated under MSI
        </h3>
        <div
          style={{
            margin: "18px 10px 18px 10px",
            display: "flex",
          }}
        >
          <div
            className="buttons"
            style={{
              background: "#F0F2F4",
              borderRadius: "3px",
              width: "4.5rem",
              marginLeft: "10px",
              height: "30px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Button
              style={{
                background: `${!activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
                marginRight: "4px",
              }}
              onClick={() => {
                setActiveMode(false);
              }}
            >
              <i className="pi pi-table fs-19" />
            </Button>
            <Button
              style={{
                background: `${activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
              }}
              onClick={() => {
                setActiveMode(true);
              }}
            >
              <i className="pi pi-chart-bar fs-19" />
            </Button>
          </div>
          <div ref={menuRef}>
            <Button
              style={{
                color: "black",
                height: "30px",
                marginLeft: "3px",
                background: "#F0F2F4",
                border: "0px",
                padding: "6px",
                position: "relative",
              }}
              onClick={() => {
                setDropdownOpen(!dropdownOpen);
              }}
            >
              <i className="pi pi-angle-down fs-19" />
            </Button>
            {dropdownOpen && (
              <Menu
                model={panelItems}
                style={{
                  position: "absolute",
                  right: 45,
                  zIndex: "1",
                  padding: 0,
                }}
              ></Menu>
            )}
          </div>
        </div>
      </div>
      <ResponsiveContainer width="100%" height={400}>
        <BarChart barSize={60} data={chartData}>
          <YAxis type="number" />
          <XAxis dataKey="region" type="category"></XAxis>
          <Tooltip />
          <Legend content={CustomLegend} />
          <Bar dataKey="totalDealers" fill="#0000cc" name="Total No of Dealers">
            <LabelList
              dataKey="totalDealers"
              position="top"
              style={{ fontSize: "12px", fill: "black" }}
            />
          </Bar>
          <Bar
            dataKey="audited"
            fill="#006600"
            name="Dealers Calibrated under MSI"
          >
            <LabelList
              dataKey="auditedPercent"
              position="top"
              formatter={(value) => `${value}%`}
              style={{ fontSize: "12px", fill: "black" }}
            />
          </Bar>

          
        </BarChart>
      </ResponsiveContainer>
    </Card>
  );
};

export default SideBarTab;
