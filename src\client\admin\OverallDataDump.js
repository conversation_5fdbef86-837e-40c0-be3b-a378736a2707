import React, { useState, useEffect, useRef } from "react";
import {
  Tab,
  Tabs,
  Box,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from "@mui/material";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import APIServices from "../../service/APIService";
import { API } from "../../constants/api_url";
import { useSelector } from "react-redux";
import { tvssection } from "../../assets/tvs/js/tvssection";
import { filterDataByTierAndLocationByLevel, filterDataByTierAndLocationByTierId, filterSubmissionsByFiscalYear, getFiscalYearsFromStartDate, getRPLuxon, getRPTextFormat, sectionsList } from "../../components/BGHF/helper";
import { convertReportingMonthsToLetters } from "../../components/BGHF/helper";
import { MultiSelect } from "primereact/multiselect";
import { Dropdown } from "primereact/dropdown";
import { Button } from "primereact/button";
import { DateTime } from "luxon";
import { Tag } from "primereact/tag";
import { Badge } from "primereact/badge";
import { BlockUI } from 'primereact/blockui';
import SupplierLCATable from "../forms/ValueChainForm/SupplierLCATable";
import useForceUpdate from "use-force-update";
import XlsxPopulate from "xlsx-populate";
import { OverlayPanel } from "primereact/overlaypanel";
export const OverallDataDump = () => {
  const [activeTab, setActiveTab] = useState(0);
  const statusref = useRef(null)
  const [filter, setFilter] = useState({ year: 0, section: 0, source: 1, form: 0, framework: [], country: 0, city: null, site: null })
  const [indifilter, setIndiFilter] = useState({ year: 0, section: 0, indicator: 0, form: 0, framework: [], entity: [] })
  const [indicatorEntityList, setIndicatorEntityList] = useState([])
  const [sapEmissions, setSapEmissions] = useState([])
  const [label1, label2, label3] = useSelector((state) => state.user.tierLabel);
  const [attribute, setAttribute] = useState([{ "title": "ATTRIBUTE 1", "data": [{ "name": "SDP78_1Name", "header": "Name" }, { "name": "SDP78_1Contact Email Id", "header": "Contact Email Id" }, { "name": "SDP78_1Fuel Name", "header": "Fuel Name" }, { "name": "SDP78_1Total Consumption", "header": "Total Consumption" }, { "name": "SDP78_1Unit", "header": "Unit" }, { "name": "SDP78_1Data Source", "header": "Data Source" }, { "name": "SDP78_2Type of Biomass Fuel", "header": "Type of Biomass Fuel" }, { "name": "SDP78_2Quantity of Fuel Consumed", "header": "Quantity of Fuel Consumed" }, { "name": "SDP78_2Unit", "header": "Unit" }, { "name": "SDP78_2Type of Biofuel", "header": "Type of Biofuel" }, { "name": "SDP78_2Quantity of Fuel Consumption", "header": "Quantity of Fuel Consumption" }, { "name": "SDP78_2Unit", "header": "Unit" }, { "name": "SDP78_2Type of Refrigerant", "header": "Type of Refrigerant" }, { "name": "SDP78_2Quantity of Refrigerant Consumed", "header": "Quantity of Refrigerant Consumed" }, { "name": "SDP78_2Unit", "header": "Unit" }, { "name": "SDP78_1Electricity Source", "header": "Electricity Source" }, { "name": "SDP78_1Electricity Consumption", "header": "Electricity Consumption" }, { "name": "SDP78_1Unit", "header": "Unit" }, { "name": "SDP78_6Name of the Third-party", "header": "Name of the Third-party" }, { "name": "SDP78_6Emission Factor of the Power Procured from Third Party (tCO2e/kWh)", "header": "Emission Factor of the Power Procured from Third Party (tCO2e/kWh)" }] }, { "title": "ATTRIBUTE 2", "data": [{ "name": "SDP78_1Name", "header": "Name" }, { "name": "SDP78_1Contact Email Id", "header": "Contact Email Id" }, { "name": "SDP78_1Source of Water", "header": "Source of Water" }, { "name": "SDP78_1Quantity of Water Withdrawn", "header": "Quantity of Water Withdrawn" }, { "name": "SDP78_1Unit", "header": "Unit" }, { "name": "SDP78_6Water Consumption Category", "header": "Water Consumption Category" }, { "name": "SDP78_6Quantity of Water", "header": "Quantity of Water" }, { "name": "SDP78_6Unit", "header": "Unit" }, { "name": "SDP78_1Water Discharge Level", "header": "Water Discharge Level" }, { "name": "SDP78_1Quantity of Water Discharged", "header": "Quantity of Water Discharged" }, { "name": "SDP78_1Unit", "header": "Unit" }, { "name": "SDP78_1Remarks", "header": "Remarks" }, { "name": "SDP78_1Water Recycled and Reused", "header": "Water Recycled and Reused" }, { "name": "SDP78_1Quantity", "header": "Quantity" }, { "name": "SDP78_1Unit", "header": "Unit" }] }, { "title": "ATTRIBUTE 4", "data": [{ "name": "SDP78_1Name", "header": "Name" }, { "name": "SDP78_1Contact Email Id", "header": "Contact Email Id" }, { "name": "SDP78_1Type of Waste Generated", "header": "Type of Waste Generated" }, { "name": "SDP78_1 Quantity of Waste Generated", "header": " Quantity of Waste Generated" }, { "name": "SDP78_1Unit", "header": "Unit" }, { "name": "SDP78_2Waste Category", "header": "Waste Category" }, { "name": "SDP78_2Type of Waste", "header": "Type of Waste" }, { "name": "SDP78_2Quantity of Waste Generated", "header": "Quantity of Waste Generated" }, { "name": "SDP78_2Unit", "header": "Unit" }, { "name": "SDP78_1Type of Waste", "header": "Type of Waste" }, { "name": "SDP78_1Quantity of Waste Recovered", "header": "Quantity of Waste Recovered" }, { "name": "SDP78_1Unit", "header": "Unit" }, { "name": "SDP78_2Category of Waste", "header": "Category of Waste" }, { "name": "SDP78_2Type of Waste Recovered", "header": "Type of Waste Recovered" }, { "name": "SDP78_2Quantity of Waste Recovered", "header": "Quantity of Waste Recovered" }, { "name": "SDP78_2Unit", "header": "Unit" }, { "name": "SDP78_1Type of Waste", "header": "Type of Waste" }, { "name": "SDP78_1Quantity of Waste Incinerated", "header": "Quantity of Waste Incinerated" }, { "name": "SDP78_1Unit", "header": "Unit" }, { "name": "SDP78_2Waste Category", "header": "Waste Category" }, { "name": "SDP78_2Type of Waste Incinerated", "header": "Type of Waste Incinerated" }, { "name": "SDP78_2Quantity of Waste Incinerated", "header": "Quantity of Waste Incinerated" }, { "name": "SDP78_2Unit", "header": "Unit" }] }, { "title": "ATTRIBUTE 5", "data": [{ "name": "SDP78_1Name", "header": "Name" }, { "name": "SDP78_1Contact Email Id", "header": "Contact Email Id" }, { "name": "SDP78_1Details of safety related incidents ", "header": "Details of safety related incidents " }, { "name": "SDP78_1Reported Output", "header": "Reported Output" }, { "name": "SDP78_1Unit", "header": "Unit" }] }, { "title": "ATTRIBUTE 6", data: [] }, { "title": "ATTRIBUTE 7", data: [] }, { "title": "ATTRIBUTE 8", data: [] }, { "title": "ATTRIBUTE 9", data: [] }])
  const [attributeColumn, setAttributeTableColumn] = useState([])
  const [assFramework, setAssFramework] = useState([])
  const [locList, setLocList] = useState({ country: [], city: [], location: [] })

  const [filteredSrf, setFilteredSrf] = useState([])
  const [rawData, setRawData] = useState([]);
  const [efassignment, setEfAssignment] = useState([]);
  const [sapData, setSapData] = useState([]);
  const [metricsData, setMetricsData] = useState([]);
  const [metricsDataBk, setMetricsDataBk] = useState([]);

  const [assignedDcf, setAssignedDcf] = useState([]);
  const [rawResponse, setRawResponse] = useState([]);
  const [metricResponse, setMetricResponse] = useState([])
  const [rawdatabk, setRawDataBk] = useState([]);
  const [filteredSapData, setFilteredSapData] = useState(sapData);
  const [selectedDcf, setSelectedDcf] = useState();
  const [selectedSap, setSelectedSap] = useState();
  const [reportingPeriod, setReportingPeriod] = useState(0);
  const [category, setCategory] = useState();
  const [dataSource, setDataSource] = useState("dcf");
  const [indicatorType, setIndicatorType] = useState(0);
  const [metricKpi, setMetricKpi] = useState("");
  const [sapDataResponse, setSapDataResponse] = useState([]);
  const [sapDropdown, setSapDropdown] = useState([]);
  const [formlist, setFormList] = useState([])
  const [load, setLoad] = useState(true);
  const [filters, setFilters] = useState({});
  const [yearOption, setYearOption] = useState([]);
  const [customMetricResponseBk, setCustomMetricResponseBk] = useState([]);
  const [customMetricResponse, setCustomMetricResponse] = useState([]);
  const [supplierSubmission, setSupplierSubmission] = useState({ current: [], backup: [] })
  const admin_data = useSelector((state) => state.user.admindetail);
  const userList = useSelector((state) => state.userlist.userList);
  const [rawsitelist, setRawsitelist] = useState([]);
  const tvsSubAdminRoles = useSelector((state) => state.user.tvsSubAdminRoles);
  const { fymonth } = useSelector((state) => state.user.fyStartMonth);
  const forceUpdate = useForceUpdate();
  const [sectionlist, setSectionList] = useState([
    { name: "All", id: 0 },
    { name: "Sustainability", id: 8 },
    { name: "Health", id: 9 },
    { name: "Safety Central", id: 10 },
    { name: "Safety Operational", id: 11 },
    { name: "Supply Chain", id: 12 },
    { name: "Dealership Sustainability", id: 13 },
    { name: "Environmental", id: 14 },
    { name: "Social", id: 15 },
    { name: "Governance", id: 16 },
  ]);
  function findKeyById(tvssection, id) {
    for (const key in tvssection) {
      if (tvssection[key].includes(id)) {
        return parseFloat(key);
      }
    }
    return 0;
  }

  const convertDateFormat = (dateString) => {
    if (dateString)
      return DateTime.fromFormat(dateString, "yyyyMMdd").toFormat("MM-yyyy");
    return null;
  };
  useEffect(() => {
    const fetchData = async () => {
      try {
        let uriString = {
          include: [
            {
              relation: "newTopics",
              scope: {
                include: [
                  {
                    relation: "newMetrics",
                    scope: { include: [{ relation: "newDataPoints" }] },
                  },
                ],
              },
            },
          ],
        };
        let assigned_dcf = [];
        const curatedIndicators = Array.from(
          new Set(
            Object.entries(tvssection).flatMap((i) =>
              [8, 9, 10, 11, 12, 13, 14, 15, 16].includes(parseFloat(i[0]))
                ? i[1]
                : []
            )
          )
        );
        let uriStringLoc = {
          include: [
            {
              relation: "locationTwos",
              scope: { include: [{ relation: "locationThrees" }] },
            },
          ],
        };
        const filter = {
          include: [
            {
              relation: "submitDcf",
              scope: {
                fields: {
                  id: true,
                  return_remarks: true,
                  approved_on: true,
                  locationId: true,
                  level: true,
                  reporter_modified_by: true,
                  reporter_modified_on: true,
                  reviewer_modified_on: true,
                  reviewer_modified_by: true,
                  self: true,
                  approved_by: true,
                  reject: true,
                  type: true,
                  edit: true,
                },
              },
            },
          ],
        };

        const encodedStructuredResponseFilter = encodeURIComponent(JSON.stringify(filter));
        const promise0 = await APIServices.get(
          API.AssignDCFClient_UP(admin_data.id)
        );

        const promise1 = await APIServices.get(
          API.Categories +
          `?filter=${encodeURIComponent(JSON.stringify(uriString))}`
        );
        const promise2 = await APIServices.get(API.DCF);
        const promise3 = await APIServices.get(
          API.LocationOne_UP(admin_data.id) +
          `?filter=${encodeURIComponent(JSON.stringify(uriStringLoc))}`
        );
        const promise4 = await APIServices.get(
          API.QN_Submit_UP(admin_data.id)
        );

        const promise5 = await APIServices.get(API.Structured_UP(admin_data.id) + `?filter=${encodeURIComponent(JSON.stringify(filter))}`);
        const promise6 = await APIServices.post(API.SapResponseCustom_UP(admin_data.id));
        const promise7 = await APIServices.get(
          API.SapCollection_UP(admin_data.id)
        );
        const promise8 = APIServices.get(API.Report_Name_Twos)
        Promise.all([
          promise0, promise1, promise2, promise3, promise4, promise5, promise6, promise7, promise8

        ]).then((values) => {
          let allframework = values[8].data.filter((i) => { return admin_data.information.report.includes(i.id) })
          setAssFramework(allframework)
          setFilter((prev) => ({ ...prev, framework: allframework.map(i => i.title) }))
          setIndiFilter((prev) => ({ ...prev, framework: allframework.map(i => i.title) }))
          let dcf_list = values[2].data;
          let tempArray = [];
          const shapedCategory = values[1].data
            ?.map((item) => {
              if (item.newTopics) {
                item.newTopics = item.newTopics.filter(
                  (topics) => topics.newMetrics && topics.newMetrics?.length > 0
                );
              }
              return item;
            })
            .filter((item) => item.newTopics && item.newTopics?.length > 0);

          let overallmetric = shapedCategory.flatMap(
            (i) => i.newTopics && i.newTopics.flatMap((x) => x.newMetrics)
          );
          if (values[0].data?.length !== 0) {
            shapedCategory
              ?.flatMap((i) => i?.newTopics)
              ?.forEach((top) => {
                if (
                  values[0].data[0]?.topic_ids.includes(top.id) &&
                  (top.tag === null || parseFloat(top.tag) === admin_data.id)
                ) {
                  top.newMetrics.forEach((met) => {
                    if (
                      Array.isArray(met.data1) && values[0].data[0].metric_ids.includes(met.id) &&
                      (met.tag === null || parseFloat(met.tag) === admin_data.id)
                    ) {
                      tempArray.push({ ...met, type: (met.data1[0].type === 0 && met.data1[0].source === 0) ? 2 : 1 });
                    }
                    if (
                      curatedIndicators.includes(met.id) &&
                      values[0].data[0].metric_ids.includes(met.id) &&
                      (met.tag === null ||
                        parseFloat(met.tag) === admin_data.id) &&
                      met.newDataPoints !== undefined
                    ) {
                      met.newDataPoints.forEach((ndp) => {
                        if (
                          Array.isArray(ndp.data1) &&
                          ndp.data1[0].datasource &&
                          typeof ndp.data1[0].datasource === "number" &&
                          dcf_list
                            .map((i) => i.id)
                            .includes(ndp.data1[0].datasource)
                        ) {
                          let dcf_index = dcf_list.findIndex(
                            (i) => i.id === ndp.data1[0].datasource
                          );
                          if (
                            !assigned_dcf
                              .map((i) => i.id)
                              .includes(ndp.data1[0].datasource) &&
                            dcf_index !== -1 &&
                            (dcf_list[dcf_index].tags === null ||
                              dcf_list[dcf_index].tags.includes(admin_data.id))
                          ) {

                            let dcf = {
                              ...dcf_list[dcf_index],
                              metricId: met.id,
                            };
                            dcf.section = findKeyById(tvssection, met.id);
                            assigned_dcf.push(dcf);
                          }
                        }
                      });
                    }
                  });
                }
              });

          }
          let attachedArray = [];
          for (let item of filterDerivedAndStandaloneWithIds(tempArray, overallmetric)) {

            attachedArray.push({ ...item, title: item.id + ' : ' + item.title, type: item?.standalone_ids?.length === 1 && item.standalone_ids.includes(item.id) ? 1 : ((item?.standalone_ids?.length === 1 && !item.standalone_ids.includes(item.id)) || (item?.standalone_ids?.length > 1)) ? 2 : 0, ...extractDcfSapIds(item.standalone_ids, overallmetric) })
          }
          ;
          setCustomMetricResponseBk(attachedArray);
          setCustomMetricResponse(attachedArray);

          let yrOptions = getFiscalYearsFromStartDate(
            admin_data.information.startdate
          );

          setAssignedDcf([...assigned_dcf.map(i => ({ ...i, title: i.id + ' :' + i.title, formCategory: 1 })), ...values[7].data.map(i => ({ ...i, id: i.sapId, title: i.sapId + ' :' + i.title, formCategory: 2 }))]);
          setYearOption(yrOptions);

          const shapedSite = values[3]?.data
            .map((item) => {
              if (item.locationTwos) {
                item.locationTwos = item.locationTwos.filter(
                  (locationTwo) =>
                    locationTwo.locationThrees &&
                    locationTwo.locationThrees?.length > 0
                );
              }
              return item;
            })
            .filter((item) => item.locationTwos && item.locationTwos?.length > 0);
          setSapEmissions(values[6].data?.data?.filter(x => x.sapId === 'SAP4' || x.sapId === 'SAP5' || x.sapId === 'SAP7')?.map(x => ({ ...x, unitOfMeasure: "USD", title: x.sapId === 'SAP7' ? x.ModeOfTransportation : x.MaterialCategory, periodFrom: getRPLuxon(x.Month)?.[0], periodTo: getRPLuxon(x.Month)?.[0], value: x.sapId === 'SAP7' ? x.InvoiceAmount : x.TotalSpent, dcfId: x.sapId === 'SAP7' ? 293 : x.sapId === 'SAP4' ? 16 : 282, reporting_period: x.Month, rp: getRPLuxon(x.Month), entity: getCoverageText(getLevelAndLocationId(x), shapedSite), ...getLevelAndLocationId(x) })))
          setLocList({ country: [{ name: 'All Countries', id: 0 }, ...shapedSite.map(location => ({ name: location.name, id: location.id }))] });
          setRawsitelist(shapedSite);

          const result2 = values[5].data.filter(i => i.submitDcf).map(i => ({
            formCategory: 1,
            formId: i.dcfId,
            value: i.isNull ? 0 : i.value,
            tags: attachedArray.find(y => y?.standalone_ids?.length === 1 && y?.dcfIds?.includes(i.dcfId))?.tags,
            actualTitle: i.title,
            title: i.label.replace(/(<([^>]+)>)/gi, "")
              ?.replace(/\n/g, " ")
              ?.replace(/&nbsp;/g, " ")
              ?.replace("&amp;", "&") || '-', approver: getUser(i.submitDcf.approved_by), approverComments: i.submitDcf?.return_remarks?.reverse()?.find(x => x.user_type === 3)?.remarks || "No Comments", dateOfApproval: i.submitDcf?.approved_on ? new Date(i.submitDcf?.approved_on).toLocaleString().split(",")[0] : '-', dcfId: i.dcfId, entity: getCoverageText(i.submitDcf, shapedSite), periodFrom: i.reporting_period?.[0] || "N/A",
            periodTo:
              i?.reporting_period[i?.reporting_period?.length - 1] ||
              "N/A",
            unitOfMeasure: i?.uom || "-",
            dataType: i?.dataType || null,
            formType: i?.formType || null,
            uniqueId: i?.uniqueId || null,
            locationId: i.submitDcf?.locationId,
            level: i.submitDcf?.level,
            reporter: `${getUser(i.submitDcf?.reporter_modified_by)}`,
            reportedDate: new Date(i.submitDcf?.reporter_modified_on)
              .toLocaleString()
              .split(",")[0],
            reporting_period: getRPTextFormat(i.reporting_period),
            rp: i.reporting_period,
            reviewedDate: i.submitDcf?.reviewer_modified_on ? new Date(i.submitDcf?.reviewer_modified_on).toLocaleString()
              .split(",")[0] : '-',
            reporterComments: i.submitDcf?.return_remarks?.reverse()?.find(x => x.user_type === 1)?.remarks || "No Comments",
            reviewer: i.submitDcf?.self ? 'Self' : ` ${getUser(i.submitDcf?.reviewer_modified_by)}`,

            efValue: i.efValue,
            submitId: i.submitDcfId,
            reviewerComments:
              i.submitDcf?.return_remarks?.reverse()?.find(x => x.user_type === 2)?.remarks || "No Comments",
            approver: i.submitDcf?.approved_by
              ? `${getUser(i.submitDcf?.approved_by)}`
              : "N/A",
            status: "Approved"
          }))

          const data = values[4]?.data;
          setRawResponse(data);


          let sapRawData = []
          if (values[6].data.result) {
            const shapedSapData = [
              ...values[6].data.data.map((item) => ({
                formId: item.sapId, formCategory: 2,
                dataPoint: item.sapId === 'SAP1' ? item.FuelType : (item.sapId === 'SAP2' || item.sapId === 'SAP3') ? item.WasteDescription : (item.sapId === 'SAP5' || item.sapId === 'SAP4') ? item.MaterialCategory : item.sapId === 'SAP6' ? item.BTOrigin + ' to ' + item.BTDestination : item.sapId === 'SAP7' ? item.ModeOfTransportation
                  : item.sapId === 'SAP8' ? item.EmpId : 'No', //NeedToConfirm
                value: (item.sapId === 'SAP5' || item.sapId === 'SAP4') ? item.TotalSpent : item.sapId === 'SAP7' ? item.InvoiceAmount : (item?.Quantity || '-'),
                unitOfMeasure: item.UoM || 'NA',
                status: "Approved",
                entity: item.Plant || 'NA',
                rp: [item.Date ? convertDateFormat(item.Date) : null].filter(x => x),
                reportingPeriodFrom: convertDateFormat(item.Date),
                reportingPeriodTo: convertDateFormat(item.Date),
                syncDate: formatIsoDate(item.fetched_on),
              })),
            ];
            sapRawData = Object.values(
              shapedSapData.reduce((acc, item) => {
                const {
                  reportingPeriodFrom,
                  dataPoint,
                  value,
                  unitOfMeasure, status,
                  entity,
                  reportingPeriodTo, formId, formCategory,
                  syncDate, rp,
                } = item;

                const key = `${reportingPeriodFrom}-${dataPoint}-${unitOfMeasure}-${entity}`;

                if (!acc[key]) {
                  acc[key] = {
                    reportingPeriodTo,
                    reportingPeriodFrom,
                    dataPoint,
                    unitOfMeasure, rp, formId, formCategory, status,
                    entity,
                    value: 0,
                    syncDate,
                  };
                }
                const parsedValue = parseFloat(value);
                if (!isNaN(parsedValue)) {
                  acc[key].value += parsedValue;
                }

                return acc;
              }, {})
            );
          }

          setRawData([...result2, ...sapRawData]);
          setRawDataBk([...result2, ...sapRawData]);

        })


      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoad(false)
      }
    };

    fetchData();
    return () => {
      // ✅ Proper cleanup function
    };
  }, []);




  function filterDerivedAndStandaloneWithIds(data, overall) {
    const childIds = new Set();
    const standaloneChildren = {};

    function collectStandaloneIds(itemId) {
      const item = overall.find((d) => d.id === itemId);
      if (!item || !item.data1[0]) return [];

      let standaloneIds = item.data1[0].indicator.filter((id) => {
        const child = overall.find((d) => d.id === id);
        return child && child.data1[0]?.source === 1;
      });

      item.data1[0].indicator.forEach((id) => {
        const child = overall.find((d) => d.id === id);
        if (child && child.data1[0]?.source === 0) {
          const nestedStandaloneIds = collectStandaloneIds(child.id);
          standaloneIds = standaloneIds.concat(nestedStandaloneIds);
        }
      });

      return standaloneIds;
    }

    function collectTags(item) {
      if (!item?.data1[0]) return [];

      // ✅ Collect tags from tags1 to tags4 and remove empty values
      return [
        ...(item.data1[0].tags1 || []),
        ...(item.data1[0].tags2 || []),
        ...(item.data1[0].tags3 || []),
        ...(item.data1[0].tags4 || []),
      ].filter(Boolean); // Remove null, undefined, or empty values
    }

    // Collect standalone children for derived parents
    data.forEach((item) => {
      if (item.data1[0]?.type === 0 && item.data1[0]?.source === 0) {
        const standaloneIds = collectStandaloneIds(item.id);
        if (standaloneIds.length > 0) {
          standaloneChildren[item.id] = standaloneIds;
        }

        // Add all child IDs (standalone or not) to the set of child IDs
        item.data1[0].indicator.forEach((id) => childIds.add(id));
      }
    });

    // Filter out derived children and attach standalone_ids + collected tags
    const filteredData = data
      .map((item) => {
        let combinedTags = collectTags(item);

        // ✅ Collect tags from standalone indicators
        if (standaloneChildren[item.id]) {
          standaloneChildren[item.id].forEach((childId) => {
            const child = overall.find((d) => d.id === childId);
            if (child) {
              combinedTags = combinedTags.concat(collectTags(child));
            }
          });

          // Remove duplicates from combined tags
          combinedTags = [...new Set(combinedTags)];

          return {
            ...item,
            standalone_ids: standaloneChildren[item.id],
            tags: combinedTags,
          };
        }

        // ✅ Check for standalone items (source = 1)
        if (item.data1[0]?.type === 0 && item.data1[0]?.source === 1) {
          return {
            ...item,
            standalone_ids: [item.id],
            tags: combinedTags,
          };
        }

        // ✅ Retain only if it's not a derived child (id not in childIds)
        if (!childIds.has(item.id)) {
          return { ...item, tags: combinedTags };
        }

        return null; // Exclude derived children
      })
      .filter((item) => item !== null);

    return filteredData;
  }



  const getLevelAndLocationId = (data) => {
    if (data.tier3_id) return { level: 3, locationId: Number(data.tier3_id) };
    if (data.tier2_id) return { level: 2, locationId: Number(data.tier2_id) };
    if (data.tier1_id) return { level: 1, locationId: Number(data.tier1_id) };
    if (data.tier0_id === 0) return { level: 0, locationId: 0 };
    return null;
  };
  useEffect(() => {
    async function fetchData() {
      if (rawData.length && efassignment.length && customMetricResponse.length) {

        let filtered = JSON.parse(JSON.stringify(rawData))
        // for (const indi of customMetricResponse) {
        //   let index = metricArray.findIndex(y => y.indicatorId === indi.id)
        //   for (const i of filtered.filter(x => indi.dcfIds.includes(x?.dcfId))) {
        //     if (i.formType === 2 && i.dataType === 1) {
        //       let lastDate = DateTime.fromFormat(i.reporting_period.split(' to ')[0], 'LLL-yyyy', { zone: 'utc' });
        //       let filteredAssignment1 = efassignment.filter(x => x.hierarchyId === i.uniqueId && x.startMonth && x.startMonth !== 'NA')

        //       const dateIndex = filteredAssignment1.findIndex((dateRange) => {
        //         const startDate = DateTime.fromFormat(dateRange.startMonth, 'LLL-yyyy');
        //         const endDate = (dateRange.endMonth && dateRange.endMonth !== "Present") ? DateTime.fromFormat(dateRange.endMonth, 'LLL-yyyy') : DateTime.local();
        //         
        //         // Step 8: Check if reporting_date + 1 is greater than the start and end of the item in newEfDates
        //         return lastDate >= startDate && lastDate <= endDate;
        //       });
        //       
        //       if (dateIndex !== -1) {
        //         let filteredLocation = filterDataByTierAndLocationByLevel([{ locationId: i.locationId, level: i.level }], rawsitelist, filteredAssignment1[dateIndex].tier1_id, filteredAssignment1[dateIndex].tier2_id, filteredAssignment1[dateIndex].tier3_id)
        //         if (filteredLocation.length > 0) {
        //           const obj = filteredAssignment1[dateIndex]
        //           i.emissionFactorName = obj.standard
        //           i.emissionFactorValue = obj.co2e
        //           i.efkey = obj.uniqueEfId
        //           i.formula = obj.methodology
        //           i.computedValue = ((i.emissionFactorValue / 1000) * i.value).toFixed(3)
        //         }
        //       } else {
        //         i.emissionFactorName = '-'
        //         i.emissionFactorValue = '-'
        //         i.efkey = "-"
        //         i.formula = "-"
        //         i.computedValue = "-"
        //       }

        //     }
        //     if (index === -1) {
        //       metricArray.push({ indicatorId: indi.id, title: indi.title, contributingEntities: [{ title: i.entity, locationId: i.locationId, level: i.level, contributingReportingPeriod: [{ title: i.reporting_period, status: i.status, contributingDataPoints: [i] }] }] })
        //     } else {
        //       let index2 = metricArray[index].contributingEntities.findIndex(y => y.locationId === i.locationId && y.level === i.level)
        //       if (index2 === -1) {
        //         metricArray[index]['contributingEntities'].push({ title: i.entity, locationId: i.locationId, level: i.level, contributingReportingPeriod: [{ title: i.reporting_period, status: i.status, contributingDataPoints: [i] }] })
        //       } else {
        //         let index3 = metricArray[index].contributingEntities[index2].contributingReportingPeriod.findIndex(y => y.title === i.reporting_period)
        //         if (index3 === -1) {
        //           metricArray[index]['contributingEntities'][index2]['contributingReportingPeriod'].push({ title: i.reporting_period, status: i.status, contributingDataPoints: [i] })
        //         } else {
        //           metricArray[index]['contributingEntities'][index2]['contributingReportingPeriod'][index3].contributingDataPoints.push(i)
        //         }

        //       }

        //     }
        //   }

        // }

        const result = await processCustomMetrics(customMetricResponse.filter(x => x.id === indifilter.indicator), [...filtered, ...sapEmissions], efassignment, rawsitelist)

        setLoad(false)
        const uniqueEntityList = Array.from(new Set(result?.flatMap(x => x.contributingEntities)?.map(x => x?.title || '').filter(x => x)))
        setIndicatorEntityList(uniqueEntityList)


        const filteredEntities = result.map(item => {
          const matchedEntities = item.contributingEntities.filter(entity =>
            indifilter.entity.includes(entity.title) || indifilter.entity.length === 0
          );
          return matchedEntities.length
            ? { ...item, contributingEntities: matchedEntities }
            : null;
        })
          .filter(Boolean)
        setIndiFilter((prev) => ({ ...prev, entity: indifilter.entity.filter(x => uniqueEntityList.includes(x)) }))
        setMetricsData(filteredEntities)
        setMetricsDataBk(filteredEntities)

      }
    }
    fetchData();
  }, [rawData, efassignment, customMetricResponse])
  function getValueByPercentage(value, percent) {
    const val = parseFloat(value);
    const pct = parseFloat(percent);

    if (isNaN(val) || isNaN(pct)) return 0;

    return (val * pct) / 100;
  }
  async function processCustomMetrics(customMetricResponse, filtered, efassignment, rawsitelist) {
    let metricArray = []

    for (const indi of customMetricResponse) {

      // Process filtered data for each metric response
      await Promise.all(
        filtered
          .filter(x => indi.dcfIds.includes(x?.dcfId))
          .map(async (i) => {
            let index = metricArray.findIndex(y => y.indicatorId === indi.id);
            // i.methodology = indi.type === 1 ? '-' : indi.standalone_ids.map(i => "MT" + i).join(' + ')
            if (i.isNull) {
              Object.assign(i, { value: 0 })
            }
            if (i.formType === 2 && i.dataType === 1) {
              let lastDate = DateTime.fromFormat(i.reporting_period.split(' to ')[0], 'LLL-yyyy', { zone: 'utc' });
              let filteredAssignment1 = []
              if (indi.id === 172) {
                const titleParts = i.actualTitle.split('>').map(s => s.trim().toLowerCase());
                filteredAssignment1 = efassignment.filter(x => x.itemId === 16 || x.itemId === 58).filter(x => {

                  const match1 = (x.subCategory1 || '')?.trim().toLowerCase().includes(titleParts[0]);
                  const match2 = (x.subCategory2 || '')?.trim().toLowerCase().includes(titleParts[1]);
                  const match3 = (x.subCategory3 || '')?.trim().toLowerCase().includes(titleParts[2]);

                  const titleMatch =
                    titleParts.length === 3 ? match1 && match2 && match3 :
                      titleParts.length === 2 ? match1 && match2 :
                        titleParts.length === 1 ? match1 : false;

                  return titleMatch && x.startMonth && x.startMonth !== 'NA';
                });


              } else {
                filteredAssignment1 = efassignment.filter(
                  x => (x.hierarchyId === i.uniqueId) && x.startMonth && x.startMonth !== 'NA'
                );
              }

              const dateIndex = filteredAssignment1.findIndex((dateRange) => {
                const startDate = DateTime.fromFormat(dateRange.startMonth, 'LLL-yyyy');
                const endDate = (dateRange.endMonth && dateRange.endMonth !== "Present")
                  ? DateTime.fromFormat(dateRange.endMonth, 'LLL-yyyy')
                  : DateTime.local();

                // Check if reporting_date falls within the range
                return lastDate >= startDate && lastDate <= endDate;
              });

              if (dateIndex !== -1) {
                let filteredLocation = filterDataByTierAndLocationByLevel(
                  [{ locationId: i.locationId, level: i.level }],
                  rawsitelist,
                  filteredAssignment1[dateIndex].tier1_id,
                  filteredAssignment1[dateIndex].tier2_id,
                  filteredAssignment1[dateIndex].tier3_id
                );

                if (filteredLocation.length > 0) {
                  const obj = filteredAssignment1[dateIndex];
                  Object.assign(i, {
                    emissionFactorName: obj.standard,
                    emissionFactorValue: obj.co2e,
                    emissionFactorCo2Value: obj.co2,
                    emissionFactorCh4Value: obj.ch4,
                    emissionFactorN2oValue: obj.n2o,
                    efkey: obj.uniqueEfId,
                    methodology: (indi.id === 172 && i.dcfId === 257) ? "Electricity Consumption * 17.68 % * Emission Factor / 1000 " : i.dcfId === 10 ? '(Quantity of Refrigerant Refilled * Emission Factor)/1000' : i.dcfId === 15 ? '1.By Fuel: (Fuel Consumption  * Emission Factors based on the Fuel used)/1000 2.By KM Driven: (Total KM Driven * Emission Factors based on the type of vehicle used)/1000' : i.dcfId === 257 ? 'Electricity Consumption* Emission Factor' : obj.methodology,
                    computedValue: (indi.id === 172 && i.dcfId === 257) ? ((obj.co2e / 1000) * getValueByPercentage(i.value, 17.68)).toFixed(3) : ((obj.co2e / 1000) * i.value).toFixed(3),
                    computedCo2Value: (indi.id === 172 && i.dcfId === 257) ? ((obj.co2 / 1000) * getValueByPercentage(i.value, 17.68)).toFixed(3) : ((obj.co2 / 1000) * i.value).toFixed(3),
                    computedCh4Value: (indi.id === 172 && i.dcfId === 257) ? ((obj.ch4 / 1000) * getValueByPercentage(i.value, 17.68)).toFixed(3) : ((obj.ch4 / 1000) * i.value).toFixed(3),
                    computedN2oValue: (indi.id === 172 && i.dcfId === 257) ? ((obj.n2o / 1000) * getValueByPercentage(i.value, 17.68)).toFixed(3) : ((obj.n2o / 1000) * i.value).toFixed(3),

                  });
                }
              } else if (i?.efValue) {

                Object.assign(i, {
                  emissionFactorName: '-',
                  efkey: '-',
                  formula: '-',
                  computedValue: i?.efValue,
                })
              } else {
                // Set default values if no valid date range is found
                Object.assign(i, {
                  emissionFactorName: '-',
                  emissionFactorValue: '-',
                  efkey: "-",
                  formula: "-",
                  computedValue: "-",
                });
              }
            } else if (i.efKey) {

              let lastDate = DateTime.fromFormat(i.reporting_period.split(' to ')[0], 'LLL-yyyy', { zone: 'utc' });
              let filteredAssignment1 = efassignment.filter(
                x => (x.hierarchyId === i.efKey)
              );

              const dateIndex = filteredAssignment1.findIndex((dateRange) => {
                const startDate = DateTime.fromFormat(dateRange.startMonth, 'LLL-yyyy');
                const endDate = (dateRange.endMonth && dateRange.endMonth !== "Present")
                  ? DateTime.fromFormat(dateRange.endMonth, 'LLL-yyyy')
                  : DateTime.local();

                // Check if reporting_date falls within the range
                return lastDate >= startDate && lastDate <= endDate;
              });



              if (dateIndex !== -1) {
                let filteredLocation = filterDataByTierAndLocationByLevel(
                  [{ locationId: i.locationId, level: i.level }],
                  rawsitelist,
                  filteredAssignment1[dateIndex].tier1_id,
                  filteredAssignment1[dateIndex].tier2_id,
                  filteredAssignment1[dateIndex].tier3_id
                );

                if (filteredLocation.length > 0) {
                  const obj = filteredAssignment1[dateIndex];
                  Object.assign(i, {
                    value: indi.id === 1642 ? getValueByPercentage(i.value, 2) : i.value,
                    emissionFactorName: obj.standard,
                    emissionFactorValue: obj.co2e,
                    emissionFactorCo2Value: obj.co2,
                    emissionFactorCh4Value: obj.ch4,
                    emissionFactorN2oValue: obj.n2o,
                    efkey: obj.uniqueEfId,
                    methodology: i.dcfId === 10 ? '(Quantity of Refrigerant Refilled * Emission Factor)/1000' : i.dcfId === 15 ? '1.By Fuel: (Fuel Consumption  * Emission Factors based on the Fuel used)/1000 2.By KM Driven: (Total KM Driven * Emission Factors based on the type of vehicle used)/1000' : i.dcfId === 257 ? 'Electricity Consumption* Emission Factor' : obj.methodology,
                    computedValue: indi.id === 1642 ? ((obj.co2e / 1000) * getValueByPercentage(i.value, 2)).toFixed(3) : ((obj.co2e / 1000) * i.value).toFixed(3),
                    computedCo2Value: indi.id === 1642 ? ((obj.co2 / 1000) * getValueByPercentage(i.value, 2)).toFixed(3) : ((obj.co2 / 1000) * i.value).toFixed(3),
                    computedCh4Value: indi.id === 1642 ? ((obj.ch4 / 1000) * getValueByPercentage(i.value, 2)).toFixed(3) : ((obj.ch4 / 1000) * i.value).toFixed(3),
                    computedN2oValue: indi.id === 1642 ? ((obj.n2o / 1000) * getValueByPercentage(i.value, 2)).toFixed(3) : ((obj.n2o / 1000) * i.value).toFixed(3),

                  });
                }
              } else {
                // Set default values if no valid date range is found
                Object.assign(i, {
                  emissionFactorName: '-',
                  emissionFactorValue: '-',
                  efkey: "-",
                  formula: "-",
                  computedValue: "-",
                });
              }
            } else {
              if (i?.efValue) {
                Object.assign(i, {
                  emissionFactorName: i?.emissionFactorName || '-',
                  efkey: i?.efKey || '-',
                  formula: i?.methodology || '-',
                  computedValue: i?.efValue,
                })
              } else {
                i.computedValue = i.value
              }
            }

            // Update metricArray
            if (index === -1) {
              // Add new indicator
              metricArray.push({
                indicatorId: indi.id,
                indicatorTitle: indi.title, type: indi?.type || null, standalone_ids: indi?.standalone_ids, unit: indi?.data1?.[0]?.unit || '-',
                contributingEntities: [{
                  entityName: i.entity,
                  locationId: i.locationId,
                  level: i.level,
                  contributingReportingPeriod: [{
                    ReportingPeriod: i.reporting_period,
                    status: i.status, periodFrom: i.periodFrom, periodTo: i.periodTo,
                    contributingDataPoints: [i],
                  }]
                }]
              });
            } else {
              // Update existing indicator
              let entityIndex = metricArray[index].contributingEntities.findIndex(
                y => y.locationId === i.locationId && y.level === i.level
              );

              if (entityIndex === -1) {
                // Add new contributing entity
                metricArray[index].contributingEntities.push({
                  entityName: i.entity,
                  locationId: i.locationId,
                  level: i.level,
                  contributingReportingPeriod: [{
                    title: i.reporting_period,
                    status: i.status, periodFrom: i.periodFrom, periodTo: i.periodTo,
                    contributingDataPoints: [i],
                  }]
                });
              } else {

                // Update existing contributing entity
                let periodIndex = metricArray[index].contributingEntities[entityIndex].contributingReportingPeriod.findIndex(
                  y => y.ReportingPeriod === i.reporting_period
                );

                if (periodIndex === -1) {
                  // Add new reporting period
                  metricArray[index].contributingEntities[entityIndex].contributingReportingPeriod.push({
                    ReportingPeriod: i.reporting_period,
                    status: i.status, periodFrom: i.periodFrom, periodTo: i.periodTo,
                    contributingDataPoints: [i],
                  });
                } else {
                  // Add data point to existing reporting period
                  metricArray[index].contributingEntities[entityIndex].contributingReportingPeriod[periodIndex].contributingDataPoints.push(i);
                }
              }
            }
          })
      );
    }
    return metricArray
  }

  const formatIsoDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-GB");
  };



  function processSingleObject(locations, locationData) {
    return locations
      .map(
        ({
          tier1_id,
          tier2_id,
          tier3_id,
          efGhgCat,
          efStandard,
          efCategory,
          efGhgSubCat,
          hierarchicalData,
          efGhgCatId,
          efStandardId,
          efCategoryId,
          efGhgSubCatId,
        }) => {
          let tier1 = "Global";
          let tier2 = "All";
          let tier3 = "All";

          if (tier1_id) {
            const tier1Data = locationData.find((loc) => loc.id === tier1_id);
            if (tier1Data) tier1 = tier1Data.name;

            if (tier2_id) {
              const tier2Data = tier1Data.locationTwos.find(
                (loc) => loc.id === tier2_id
              );
              if (tier2Data) {
                tier2 = tier2Data.name;

                if (tier3_id) {
                  const tier3Data = tier2Data.locationThrees.find(
                    (loc) => loc.id === tier3_id
                  );
                  if (tier3Data) tier3 = tier3Data.name;
                }
              }
            }
          }

          return hierarchicalData.map((x) => ({
            ...x,
            ...{
              uniqueEfId:
                `S${efStandardId}-${x?.dateId ? "T" + x.dateId : "NA"
                }-G${efGhgCatId}-GS${efGhgSubCatId}-I${efCategoryId}-` +
                x.hierarchyId, uniqueId: x.hierarchyId,
              startMonth: x?.startDate || "NA",
              endMonth: x?.endDate || "NA",
              methodology: x?.methodology || "Not Found",
              tier1_id,
              tier2_id,
              tier3_id,
              tier1,
              tier2,
              tier3,
              standard: efStandard?.title || "Not Found",
              ghgcategory: efGhgCat?.title || "Not Found",
              ghgsubcategory: efGhgSubCat?.title || "Not Found",
              itemId: efCategory?.id || "",
              item: efCategory?.title || "Not Found",
              co2e: x?.co2e || "-",
              co2: x?.co2 || "-",
              ch4: x?.ch4 || "-",
              n2o: x?.n2o || "-",
            },
          }));
        }
      )
      .reduce((a, b) => [...a, ...b], []);
  }

  const convertSectionIdToName = (id) => {
    return sectionsList?.find((i) => i.id === id).title;
  };
  const convertSectionNameToId = (name) => {
    return sectionsList?.find((i) => i.title === name).id;
  };

  const getCoverageText = (rowData, rawsitelist) => {
    let text = "";

    if (rowData.level === 0) {
      text = "Corporate";
    } else if (rowData.level === 1) {
      let country_index = rawsitelist.findIndex(
        (i) => i.id === rowData.locationId
      );
      if (country_index !== -1) {
        text = rawsitelist[country_index].name;
      }
    } else if (rowData.level === 2) {
      let city_index = rawsitelist
        ?.flatMap((i) =>
          i.locationTwos?.flatMap((j) =>
            j.locationThrees?.map((k) => {
              return {
                site_id: k.id,
                site_name: k.name,
                city_id: j.id,
                city_name: j.name,
                country_id: i.id,
                country_name: i.name,
              };
            })
          )
        )
        .findIndex((i) => {
          return i.city_id === rowData.locationId;
        });
      if (city_index !== -1) {
        text = rawsitelist.flatMap((i) =>
          i.locationTwos.flatMap((j) =>
            j.locationThrees.map((k) => {
              return {
                site_id: k.id,
                site_name: k.name,
                city_id: j.id,
                city_name: j.name,
                country_id: i.id,
                country_name: i.name,
              };
            })
          )
        )[city_index].city_name;
      }
    } else if (rowData.level === 3) {
      let site_index = rawsitelist
        .flatMap((i) =>
          i.locationTwos.flatMap((j) =>
            j.locationThrees.map((k) => {
              return {
                site_id: k.id,
                site_name: k.name,
                city_id: j.id,
                city_name: j.name,
                country_id: i.id,
                country_name: i.name,
              };
            })
          )
        )
        .findIndex((i) => {
          return i.site_id === rowData.locationId;
        });
      if (site_index !== -1) {
        text = rawsitelist.flatMap((i) =>
          i.locationTwos.flatMap((j) =>
            j.locationThrees.map((k) => {
              return {
                site_id: k.id,
                site_name: k.name,
                city_id: j.id,
                city_name: j.name,
                country_id: i.id,
                country_name: i.name,
              };
            })
          )
        )[site_index].site_name;
      }
    }
    return text;
  };


  const updateDataByFilter = (obj, value, obj2) => {
    let loc = { ...filter, [obj]: value }

    if (loc.year === 0) {
      const locFilter = getLocationFilterValue(obj2, value,loc)
      loc = { ...loc, ...locFilter }
      const filteredData = rawdatabk.filter(x => (x.formCategory === 1 ? x?.tags?.some(tag => {
        return loc.framework.some(searchTerm => tag.trim().toLowerCase().includes(searchTerm.trim().toLowerCase()));

      }) : true) && (x.section === loc.section || loc.section === 0) && (x.formCategory === loc.source) && (x.formId === loc.form || loc.form === 0))
      setRawData(filterDataByTierAndLocationByLevel(filteredData, rawsitelist, loc.country, loc.city, loc.site))
    } else {
      if (obj === 'location') {
        const locFilter = getLocationFilterValue(obj2, value,loc)
        loc = { ...loc, ...locFilter }
        console.log(loc)
        const filteredData = filterSubmissionsByFiscalYear(rawdatabk.filter(x => (x.formCategory === 1 ? x?.tags?.some(tag => {
          return loc.framework.some(searchTerm => tag.trim().toLowerCase().includes(searchTerm.trim().toLowerCase()));

        }) : true) && (x.section === loc.section || loc.section === 0) && (x.formCategory === loc.source) && (x.formId === loc.form || loc.form === 0)), loc.year, fymonth, 'rp')
        setRawData(filterDataByTierAndLocationByLevel(filteredData, rawsitelist, loc.country, loc.city, loc.site))
      } else {
        const filteredData = filterSubmissionsByFiscalYear(rawdatabk.filter(x => (x.formCategory === 1 ? x?.tags?.some(tag => {
          return loc.framework.some(searchTerm => tag.trim().toLowerCase().includes(searchTerm.trim().toLowerCase()));

        }) : true) && (x.section === loc.section || loc.section === 0) && (x.formCategory === loc.source) && (x.formId === loc.form || loc.form === 0)), loc.year, fymonth, 'rp')
        setRawData(filterDataByTierAndLocationByLevel(filteredData, rawsitelist, loc.country.country, loc.city, loc.site))
      }

    }
    forceUpdate()
    setFilter(() => loc)

  }
  const getLocationFilterValue = (obj, val,filter) => {

    let item = { ...filter, [obj]: val }
    let selected_item = { country: 0, city: 0, location: 0 }
    let country_list = [{ name: 'All Countries', id: 0 }]
    let city_list = [{ name: 'All Regions', id: 0 }]
    let location_list = [{ name: 'All Business Unit', id: 0 }]
    rawsitelist.forEach((country) => {
      country_list.push({ name: country.name, id: country.id })
      if (country.id === item.country || item.country === 0) {
        if (country.locationTwos) {
          country.locationTwos.forEach((city) => {
            city_list.push({ name: city.name, id: city.id })
            if (city.id === item.city || item.city === 0) {
              if (city.locationThrees) {
                city.locationThrees.forEach((site) => {
                  location_list.push({ name: site.name, id: site.id })

                })
              }
            }
          })

        }

      }

    })
    if (obj === 'country') {
      item.city = val === 0 ? null : 0

      item.site = null
    }
    else if (obj === 'city') {

      item.site = val === 0 ? null : 0
    }
console.log(item)

    setLocList((prev) => ({ ...prev, 'country': country_list, 'city': city_list, 'location': location_list }))
    return { country: item.country, city: item.city, site: item.site }
  }

  const getUser = (id) => {
    if (id === admin_data.id) {
      return "Enterprise Admin";
    }
    return userLookup[id] ? userLookup[id].information.empname : "";
  };
  const userLookup = userList.reduce((acc, user) => {
    acc[user.id] = user;
    return acc;
  }, {});


  const groupSubmissionIntoAttributes = (submissions) => {
    const combinedData = submissions.flatMap(submission => submission?.response?.map(x => ({ ...x, vendorId: submission.vendorId, supplierName: submission?.vendor?.supplierName, reporting_period: submission.reporting_period })) || []);

    const result = [];
    let currentAttribute = null;

    combinedData.forEach((item) => {
      if (item.type === "paragraph" && item.label.includes("ATTRIBUTE")) {
        if (currentAttribute) {
          result.push(currentAttribute);
        }

        currentAttribute = {
          title: item.label?.split(':')[0],
          vendorId: item.vendorId,
          supplierName: item.supplierName,
          reportingFrom: DateTime.fromFormat(item?.reporting_period[0] || null, 'MM-yyyy',).toFormat('LLL-yyyy'),
          reportingTo: DateTime.fromFormat(item?.reporting_period.slice(-1)[0] || null, 'MM-yyyy',).toFormat('LLL-yyyy'),
          data: []
        };
      } else if (currentAttribute) {
        const result = parseData(item)
        if (Array.isArray(result) && result.length) {
          currentAttribute.data.push(...result.filter(x => Object.keys(x).length));
        }

      }
    });

    if (currentAttribute) {
      result.push(currentAttribute);
    }

    return result;
  };
  const groupSRFIntoAttributes = (submissions) => {
    const combinedData = submissions

    const result = [];
    let currentAttribute = null;

    combinedData.forEach((item) => {
      if (item.type === "paragraph" && item.label.includes("ATTRIBUTE")) {
        if (currentAttribute) {
          result.push(currentAttribute);
        }

        currentAttribute = {
          title: item.label?.split(':')[0],
          data: []
        };
      } else if (currentAttribute) {
        if (item.type === 'table' || item.type === 'tableadd') {

          const result = item?.value?.map(x => item.name + x)
          if (Array.isArray(result) && result.length) {
            currentAttribute.data.push(...result);
          }
        }


      }
    });

    if (currentAttribute) {
      result.push(currentAttribute);
    }

    return result;
  };
  const parseData = (obj) => {
    if (!obj || !['table', 'tableadd'].includes(obj.type) || !obj.data) return [];

    return obj.data.map(row => {
      let extractedRow = {};

      if (obj.type === 'table') {
        obj.value.forEach(key => {
          const cell = row[key];
          const uniqueKey = (obj?.name || '') + key
          if (cell) {
            if (cell.type === 4) {
              // Type 4: Extract label from values based on value match
              const selectedValue = cell.data?.values?.find(v => v.value === cell.data.value);
              extractedRow[uniqueKey] = selectedValue ? selectedValue.label : '';
            } else if (cell.type === 5) {
              // Type 5: Take label from data
              extractedRow[uniqueKey] = cell.data?.label || '';
            } else if (cell.type === 3) {
              // Type 3: Take value directly
              extractedRow[uniqueKey] = cell.data?.value || 0;
            } else if (cell.type === 1) {
              // Type 1: Take value directly
              extractedRow[uniqueKey] = cell.data?.value || '';
            } else {
              extractedRow[uniqueKey] = '';
            }
          }
        });
      }

      if (obj.type === 'tableadd') {
        for (const [key, value] of Object.entries(row)) {
          const uniqueKey = (obj?.name || '') + key
          if (value?.data && value?.data.value != null) {
            if (value.type === 4) {
              // Match value to the corresponding label
              const match = value.data?.values?.find(v => v.value === value.data.value);
              extractedRow[uniqueKey] = match ? match.label : null;
            } else {
              extractedRow[uniqueKey] = value.data.value;
            }
          }
        }
      }

      return extractedRow;
    });
  };
  const renderSupplierResponse = async () => {
    setLoad(true)
    try {
      const promise3 = await APIServices.get(API.ValueChainSubmission_UP(admin_data.id) +
        `?filter=${encodeURIComponent(JSON.stringify({ where: { srfId: 78 }, fields: { id: true, vendorId: true, response: true, reporting_period: true, type: true }, include: [{ relation: 'vendor', scope: { fields: { supplierName: true, code: true } } }] }))}`)
      const groupedData = groupSubmissionIntoAttributes(promise3.data)

      setSupplierSubmission({ current: groupedData, backup: groupedData })


    } catch {

    } finally {
      setLoad(false)
    }


  }
  const renderEmissionFactor = async () => {
    setIndiFilter((prev) => ({ ...prev, indicator: customMetricResponse[0]?.id }))
    setLoad(true)
    try {
      // await APIServices.get(API.SAP_Stationary_Emissions).then((res) => {
      //   setSapEmissions(res.data)
      // })
      await APIServices.get(API.Client_EF_Mapping_UP(admin_data.id))
        .then((res) => {
          setEfAssignment(processSingleObject(res.data, rawsitelist));


        })

    } catch {
      setEfAssignment([]);
    } finally {
      setLoad(false)
    }
  }
  function RenderTable(data) {

    return (
      <BlockUI blocked={customMetricResponseBk.length === 0}>
        <div className="p-datatable p-component p-datatable-gridlines p-datatable-responsive-scroll custom-datatable p-datatable-striped" data-scrollselectors=".p-datatable-wrapper" data-pc-name="datatable" data-pc-section="root" pr_id_5="">
          <div className="p-datatable-wrapper" data-pc-section="wrapper">
            <table className="p-datatable-table p-datatable-scrollable-table" role="table" data-pc-section="table" style={{ border: 2, borderCollapse: "collapse", width: "100%" }}>
              <thead className="p-datatable-thead" data-pc-section="thead">
                <tr>
                  <th >Indicator</th>
                  <th>Type</th>
                  <th><div>Contributing Entities  <span className="ml-1" > <i style={{ background: indifilter.entity?.length !== 0 && 'aliceblue' }} className={"cur-pointer fs-18 pi " + (indifilter.entity?.length !== 0 ? 'pi-filter-slash' : 'pi-filter')} onClick={(e) => statusref.current.toggle(e)} ></i> </span>
                    <OverlayPanel ref={statusref} style={{ width: 250 }} >

                      <MultiSelect value={indifilter.entity} options={indicatorEntityList} onChange={(e) => { setIndiFilter((prev) => ({ ...prev, 'entity': e.value })) }}
                        placeholder="Select Status" className="w-full md" panelClassName={'hidefilter'} />

                      <div className="col-12 mt-2 flex justify-content-between ">
                        <div >
                          <Button label='Clear' disabled={!indifilter.entity.length} outlined onClick={() => { updateDataByIndicatorFilter('entity', []) }} />
                        </div>
                        <div >
                          <Button label='Apply' disabled={!indifilter.entity.length} onClick={() => { statusref.current.toggle(false); updateDataByIndicatorFilter('entity', indifilter.entity) }} />
                        </div>
                      </div>
                    </OverlayPanel>

                  </div>   </th>
                  <th>Approval Status</th>
                  <th>From</th>
                  <th>To</th>
                  <th>Contributing Data Points</th>
                  <th>Total Quantity </th>
                  <th>Unit of Measure</th>
                  <th>Emission Factor (if applicable)</th>
                  <th>Emission Factor ID</th>
                  <th>Emission Factor Value of Co2e in kgCO2e</th>
                  <th>Emission Factor Value of Co2 in kgCO2e</th>
                  <th>Emission Factor Value of Ch4 in kgCO2e</th>
                  <th>Emission Factor Value of N2o in kgCO2e</th>
                  <th>Formula</th>
                  <th>Computed Value in tCo2e</th>
                  <th>Computed Value in tCo2</th>
                  <th>Computed Value in tCh4</th>
                  <th>Computed Value in tN2o</th>
                  <th>Unit Of Measure</th>
                  <th>Entity Summary</th>
                  <th>Enterprise Summary</th>
                </tr>
              </thead>
              <tbody className="p-datatable-tbody" data-pc-section="tbody">
                {data.map((indicator, indicatorIndex) => {
                  let indicatorRowSpan = indicator?.contributingEntities?.reduce(
                    (sum, entity) =>
                      sum +
                      entity.contributingReportingPeriod.reduce(
                        (innerSum, period) =>
                          innerSum + period.contributingDataPoints.length,
                        0
                      ),
                    0
                  );
                  let entityCount = 0;
                  let periodCount = 0;
                  let contributingPoint = 0;


                  return indicator?.contributingEntities?.map((entity, entityIndex) => {
                    const entityRowSpan = entity.contributingReportingPeriod.reduce(
                      (sum, period) => sum + period.contributingDataPoints.length,
                      0
                    );
                    entityCount += entityRowSpan;




                    return entity?.contributingReportingPeriod?.map(
                      (period, periodIndex) => {
                        const periodRowSpan = period.contributingDataPoints.length;
                        periodCount += periodRowSpan;


                        return period?.contributingDataPoints?.map(
                          (dataPoint, dataPointIndex) => {
                            contributingPoint += 1;

                            return (<tr key={`${indicatorIndex}-${entityIndex}-${periodIndex}-${dataPointIndex}`}>
                              {/* Merge rows for Indicator and Type */}
                              {entityIndex === 0 &&
                                periodIndex === 0 &&
                                dataPointIndex === 0 && (
                                  <>
                                    <td className={'datatable-td-bottom'} rowSpan={indicatorRowSpan}>
                                      {'MT' + indicator.indicatorId + ' :' + indicator.indicatorTitle}
                                    </td>
                                    <td className={'datatable-td-bottom'} rowSpan={indicatorRowSpan}>
                                      {indicator.type === 1 ? 'Standalone' : 'Derived'}
                                    </td>
                                  </>
                                )}

                              {/* Merge rows for Contributing Entities */}
                              {periodIndex === 0 && dataPointIndex === 0 && (
                                <td className={entityCount === indicatorRowSpan ? 'datatable-td-bottom' : ''} rowSpan={entityRowSpan}>{entity.entityName}</td>
                              )}

                              {/* Merge rows for Contributing Reporting Period */}
                              {dataPointIndex === 0 && (
                                <>
                                  <td rowSpan={periodRowSpan} className={periodCount === indicatorRowSpan ? 'datatable-td-bottom' : ''}>
                                    {period.status || ""}
                                  </td>
                                  <td rowSpan={periodRowSpan} className={periodCount === indicatorRowSpan ? 'datatable-td-bottom' : ''}>
                                    {period.periodFrom || ""}
                                  </td>
                                  <td rowSpan={periodRowSpan} className={periodCount === indicatorRowSpan ? 'datatable-td-bottom' : ''}>
                                    {period.periodTo || ""}
                                  </td>
                                </>
                              )}

                              {/* Data points (no row merging) */}
                              <td className={contributingPoint === indicatorRowSpan ? 'datatable-td-bottom' : ''}>{dataPoint.title || ""}</td>
                              <td className={contributingPoint === indicatorRowSpan ? 'datatable-td-bottom' : ''}>{dataPoint.value ?? "-"}</td>
                              <td className={contributingPoint === indicatorRowSpan ? 'datatable-td-bottom' : ''}>{dataPoint.unitOfMeasure || "-"}</td>
                              <td className={contributingPoint === indicatorRowSpan ? 'datatable-td-bottom' : ''}>{dataPoint.emissionFactorName || "-"}</td>
                              <td className={contributingPoint === indicatorRowSpan ? 'datatable-td-bottom' : ''}>{dataPoint.efkey || "-"}</td>
                              <td className={contributingPoint === indicatorRowSpan ? 'datatable-td-bottom' : ''}>{dataPoint.emissionFactorValue ?? "-"}</td>
                              <td className={contributingPoint === indicatorRowSpan ? 'datatable-td-bottom' : ''}>{dataPoint.emissionFactorCo2Value ?? "-"}</td>
                              <td className={contributingPoint === indicatorRowSpan ? 'datatable-td-bottom' : ''}>{dataPoint.emissionFactorCh4Value ?? "-"}</td>
                              <td className={contributingPoint === indicatorRowSpan ? 'datatable-td-bottom' : ''}>{dataPoint.emissionFactorN2oValue ?? "-"}</td>

                              <td className={contributingPoint === indicatorRowSpan ? 'datatable-td-bottom' : ''}>{indicator.type === 2 ? indicator?.standalone_ids?.map(i => "MT" + i).join(' + ') : indicator.type === 1 ? dataPoint.methodology : "-"}</td>
                              <td className={contributingPoint === indicatorRowSpan ? 'datatable-td-bottom' : ''}>{dataPoint.computedValue ?? "-"}</td>
                              <td className={contributingPoint === indicatorRowSpan ? 'datatable-td-bottom' : ''}>{dataPoint.computedCo2Value ?? "-"}</td>
                              <td className={contributingPoint === indicatorRowSpan ? 'datatable-td-bottom' : ''}>{dataPoint.computedCh4Value ?? "-"}</td>
                              <td className={contributingPoint === indicatorRowSpan ? 'datatable-td-bottom' : ''}>{dataPoint.computedN2oValue ?? "-"}</td>

                              {entityIndex === 0 &&
                                periodIndex === 0 &&
                                dataPointIndex === 0 && (
                                  <td className={'datatable-td-bottom'} rowSpan={indicatorRowSpan}>{indicator.unit}</td>

                                )}
                              {
                                periodIndex === 0 &&
                                dataPointIndex === 0 &&
                                (<>

                                  <td className={entityCount === indicatorRowSpan ? 'datatable-td-bottom' : ''} rowSpan={entityRowSpan}>{indicator?.contributingEntities[entityIndex]?.contributingReportingPeriod.flatMap((x) => x?.contributingDataPoints).reduce((a, b) => { return (a + parseFloat(b?.computedValue)) }, 0)}</td>
                                </>
                                )}

                              {entityIndex === 0 &&
                                periodIndex === 0 &&
                                dataPointIndex === 0 && (
                                  <td className={'datatable-td-bottom'} rowSpan={indicatorRowSpan}>{indicator?.contributingEntities?.flatMap((x) => x?.contributingReportingPeriod && x?.contributingReportingPeriod?.flatMap(y => y?.contributingDataPoints)).reduce((a, b) => { return (a + parseFloat(b?.computedValue)) }, 0)}</td>

                                )}
                            </tr>)
                          }
                        );
                      }
                    );
                  });
                })}
              </tbody>
            </table>
          </div>  </div>
      </BlockUI>
    );
  }



  function extractDcfSapIds(standaloneIds, data) {
    let dcfIds = new Set();  // Using Set to ensure uniqueness
    let sapIds = new Set();

    standaloneIds.forEach(id => {
      // Find matching data for the given id in the second input (input2)
      const metric = data.find(item => item.id === id);
      if (metric && metric.newDataPoints) {
        // Iterate over newDataPoints for the current metric
        metric.newDataPoints.forEach(dataPoint => {
          // Add sapIds (assuming `dataPoint.dcf` is the source for these)
          dataPoint.data1 && Array.isArray(dataPoint.data1) && dataPoint.data1.forEach(point => {
            // Add dcfIds from `datasource`
            if (point.datasource) {
              dcfIds.add(point.datasource) // Assuming `datasource` holds the dcfId
            }

            // Add sapIds from `datasource2`
            if (point.datasource2) {
              sapIds.add(point.datasource2) // Assuming `datasource2` holds the sapId
            }
          });
        });
      }
    });

    return { sapIds: Array.from(sapIds), dcfIds: Array.from(dcfIds) };
  }

  const transformedSapDropdown = sapDropdown?.map((item) => ({
    label: `${item.sapId}-${item.title}`,
    value: item.sapId,
  }));

  const DataCollectionIdOptions =
    filter.source === 2
      ? transformedSapDropdown
      : Array.from(
        new Map(
          assignedDcf.filter(Boolean).map((item) => [item.id, item])
        ).values()
      ).map((item) => ({
        label: `DCF${item.id}-${item.title}`,
        value: item.id,
      }));

  const filterTemplate = (fieldName) => {
    const uniqueOptions = Array.from(
      new Set(rawData?.filter(x => x.formCategory === filter.source).map((item) => item[fieldName]))
    ).filter((item) => item)


    const allOptions = uniqueOptions.map((option, index) => ({
      name: option,
      id: option,
    }));

    return (options) => (
      <MultiSelect
        panelClassName="hidefilter"
        value={options.value}
        options={allOptions}
        optionLabel="name"
        optionValue="id"
        filter
        placeholder="Any"
        className="p-column-filter"
        maxSelectedLabels={1}
        style={{ minWidth: "14rem" }}
        onChange={(e) => {
          handleFilterChange(fieldName, options.filterCallback)(e.value);
        }}
      />
    );
  };

  const handleFilterChange =
    (fieldName, filterCallback) => (selectedValues) => {
      const safeValues = Array.isArray(selectedValues) ? selectedValues : [];
      filterCallback(safeValues);
    };

  const exportReport = (data, columns, fileName = "report.csv") => {
    if (!data || data.length === 0) {
      alert("No data available to export!");
      return;
    }

    // Extract headers from columns
    const headers = columns.map((col) => col.header);

    // Generate CSV rows
    const csvRows = [];
    csvRows.push(headers.join(",")); // Add header row

    // Add data rows
    data.forEach((row) => {
      const rowData = columns.map((col) => {
        const value = row[col.field] || ""; // Safely access field values
        return `"${value.toString().replace(/"/g, '""')}"`; // Escape double quotes
      });
      csvRows.push(rowData.join(","));
    });

    // Create CSV string
    const csvString = csvRows.join("\n");

    // Create a Blob and download the file
    const blob = new Blob([csvString], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);

    const link = document.createElement("a");
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();

    // Clean up
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  const rawDcfDataColumns = [
    // { field: "sno", header: "SNo" },
    { field: "title", header: "Quantitative Data Point" },
    { field: "value", header: "Quantitative Value" },
    { field: "unitOfMeasure", header: "Unit of Measure" },
    { field: "periodFrom", header: "Reporting Period (From)" },
    { field: "periodTo", header: "Reporting Period (To)" },
    { field: "entity", header: "Reporting Entity" },
    { field: "status", header: "Current Status" },
    { field: "reporter", header: "Reporter" },
    { field: "reportedDate", header: "Reported Date" },
    { field: "reporterComments", header: "Reporter Comments" },
    { field: "reviewer", header: "Reviewer" },
    { field: "reviewedDate", header: "Reviewed Date" },
    { field: "reviewerComments", header: "Reviewer Comments" },
    { field: "approver", header: "Approver" },
    { field: "dateOfApproval", header: "Date of Approval" },
    { field: "approverComments", header: "Approver Comments" },
  ];

  const rawSapDataColumns = [
    // { field: "sno", header: "SNo" },
    { field: "dataPoint", header: "Quantitative Data Point" },
    { field: "value", header: "Quantitative Value" },
    { field: "unitOfMeasure", header: "Unit of Measure" },
    { field: "reportingPeriodFrom", header: "Reporting Date (From)" },
    { field: "reportingPeriodTo", header: "Reporting Date (To)" },
    { field: "entity", header: "Reporting Entity" },
    { field: "syncDate", header: "Sync Date" },
  ];

  const metricsColumns = [
    { field: "reporting_period", header: "Reporting Frequencies" },
    { field: "entity", header: "Contributing Entities" },
    { field: "title", header: "Contributing Data Points" },
    { field: "value", header: "Total Quantity" },
    { field: "unitOfMeasure", header: "Unit of Measure" },
    { field: "emissionFactorName", header: "Emission Factor Name" },
    { field: "emissionFactorValue", header: "Emission Factor Value" },
    { field: "formula", header: "Formula" },
    { field: "computedValue", header: "Computed Value" },
    { field: "efkey", header: "EF Id" },
    { field: "entitySummary", header: "Entity Summary for Time Interval" },
  ];

  const emissionsColumns = [
    { header: label1, field: "tier1" },
    { header: label2, field: "tier2" },
    { header: label3, field: "tier3" },
    { header: "EF_ID", field: "uniqueEfId" },
    { header: "Standard", field: "standard" },
    { header: "Start Month", field: "startMonth" },
    { header: "End Month", field: "endMonth" },
    { header: "GHG Category", field: "ghgcategory" },
    { header: "GHG SubCategory", field: "ghgsubcategory" },
    { header: "Item", field: "item" },
    { header: "Item Category1", field: "subCategory1" },
    { header: "Item Category2", field: "subCategory2" },
    { header: "Item Category3", field: "subCategory3" },
    { header: "Item Category4", field: "subCategory4" },
    { header: "Co2e in kg", field: "co2e" },
  ];

  const bodyTemplate = (rowData) => {
    if (
      rowData.status === "Approved" ||
      rowData.status === "Pending Approval" ||
      rowData?.status === "Pending Review" ||
      rowData?.status === "Resubmission Required"
    ) {
      return (
        <Badge
          style={{ width: 100 }}
          value={rowData.status}
          severity={
            rowData.status === "Approved"
              ? "success"
              : rowData.status === "Pending Approval"
                ? "info"
                : rowData?.status === "Pending Review"
                  ? "info"
                  : rowData?.status === "Resubmission Required"
                    ? "warning"
                    : null
          }
        />
      );
    }
    return null;
  };

  const renderDataTable = (data, columns) => (
    <>
      <DataTable
        value={data}
        paginator
        rows={10}
        scrollable
        className="custom-datatable"
        removableSort
        rowsPerPageOptions={[10, 20, 50, 100, 150]}
        loading={load}
        filters={{
          title: { value: null, matchMode: "in" },
          dataPoint: { value: null, matchMode: "in" },
          entity: { value: null, matchMode: "in" },
          periodFrom: { value: null, matchMode: "in" },
          reportingPeriodFrom: { value: null, matchMode: "in" },
          status: { value: null, matchMode: "in" },
          reporter: { value: null, matchMode: "in" },
          reviewer: { value: null, matchMode: "in" },
          approver: { value: null, matchMode: "in" },
          unitOfMeasure: { value: null, matchMode: "in" },
        }}
      >
        {columns.map((col, index) => {
          if (
            col.field === "title" ||
            col.field === "label" ||
            col.field === "dataPoint" ||
            col.field === "entity" ||
            col.field === "periodFrom" ||
            col.field === "reportingPeriodFrom" ||
            col.field === "status" ||
            col.field === "reporter" ||
            col.field === "reviewer" ||
            col.field === "approver" ||
            col.field === "unitOfMeasure" ||
            col.field === "efkey"
          ) {
            return (
              <Column
                key={index}
                field={col.field}
                header={col.header}
                filter
                filterElement={filterTemplate(col.field)}
                showFilterMatchModes={false}
                showFilterMenuOptions={false}


                body={col.field === "status" ? bodyTemplate : (rowData) => { return <span className={col.field === 'title' && 'fw-6 cur-pointer clr-navy fs-14 text-underline'} onClick={() => { if (col.field === 'title') { window.open(window.origin + '/data_input_status/' + rowData.dcfId + '/' + rowData.submitId) } }}  >{rowData[col.field]} </span> }}
                sortable={
                  col.field === "periodFrom" ||
                  col.field === "reportingPeriodFrom"
                }
              // showApplyButton={false}
              // showAddButton={false}
              // showClearButton={false}
              />
            );
          }
          return (
            <Column
              key={index}
              field={col.field}
              header={col.header}
              sortable={col.field === "value"}
            />
          );
        })}
      </DataTable>
    </>
  );
  const updateDataByIndicatorFilter = async (obj, val) => {
    let loc = { ...indifilter, [obj]: val }

    let filtered = filterSubmissionsByFiscalYear(JSON.parse(JSON.stringify(rawdatabk)), loc.year, fymonth, 'rp')
    let sapFiltered = filterSubmissionsByFiscalYear(JSON.parse(JSON.stringify(sapEmissions)), loc.year, fymonth, 'rp')

    const result = await processCustomMetrics(customMetricResponse.filter(i => loc.indicator === i.id && i?.tags?.some(tag => {
      return loc.framework.some(searchTerm => tag.trim().toLowerCase().includes(searchTerm.trim().toLowerCase()));

    })), loc.year === 0 ? [...JSON.parse(JSON.stringify(rawdatabk)), ...sapEmissions] : [...filtered, ...sapFiltered], efassignment, rawsitelist)
    const uniqueEntityList = Array.from(new Set(result?.flatMap(x => x.contributingEntities)?.map(x => x?.title || '').filter(x => x)))
    setIndicatorEntityList(uniqueEntityList)
    const locFilter = { ...loc, entity: loc.entity.filter(x => uniqueEntityList.includes(x)) }

    const filteredEntities = result.map(item => {
      const matchedEntities = item.contributingEntities.filter(entity =>
        locFilter.entity.includes(entity.title) || locFilter.entity.length === 0
      );
      return matchedEntities.length
        ? { ...item, contributingEntities: matchedEntities }
        : null;
    })
      .filter(Boolean)
    setIndiFilter(locFilter)
    setIndicatorType(val)
    setMetricsData(filteredEntities)
    setMetricsDataBk(filteredEntities)

  }
  const RowFilterTemplate = (options, obj) => {
    return (
      <MultiSelect
        value={options.value}
        options={Array.from(new Set(efassignment.map((i) => i[obj])))}
        onChange={(e) => options.filterCallback(e.value)}
        placeholder="Any"
        className="p-column-filter"
        maxSelectedLabels={1}
        style={{ minWidth: "14rem" }}
      />
    );
  };
  const rowGroupTemplate = (rowData) => {
    return (
      <React.Fragment>
        <div>
          <strong>{rowData.supplierName}</strong> | From: {rowData.reportingFrom} | To: {rowData.reportingTo}
        </div>
      </React.Fragment>
    );
  };
  const exportIndicatorReport_ = async (data) => {
    const workbook = await XlsxPopulate.fromBlankAsync();
    const sheet = workbook.sheet(0);

    let row = 1;

    // Updated Headers
    const headers = [
      "Indicator", "Type", "Contributing Entities", "Approval Status",
      "From", "To", "Contributing Data Points", "Total Quantity",
      "Unit of Measure", "Emission Factor (if applicable)",
      "Emission Factor ID", "Emission Factor Value of Co2e in kgCO2e", "Emission Factor Value of C2o in kgCO2e", "Emission Factor Value of Ch4 in kgCO2e", "Emission Factor Value of N2o in kgCO2e",
      "Formula", "Computed Value in tCo2e", "Computed Value in tCo2", "Computed Value in tCh4", "Computed Value in tN2o", "Unit Of Measure", "Entity Summary", "Enterprise Summary"
    ];

    // Apply Header Styling (Without Font)
    headers.forEach((header, i) => {
      sheet.cell(row, i + 1).value(header).style({
        border: true,
        horizontalAlignment: "center",
        verticalAlignment: "center"
      });
    });

    row++;

    data.forEach((item) => {
      const indicatorStartRow = row;
      let totalComputedValue = 0;

      item.contributingEntities.forEach((entity) => {
        const entityStartRow = row;
        let entityComputedValue = 0;

        entity.contributingReportingPeriod.forEach((period) => {
          const periodStartRow = row;

          period.contributingDataPoints.forEach((dataPoint) => {
            sheet.cell(row, 4).value(dataPoint.status); // Approval Status
            sheet.cell(row, 5).value(dataPoint.periodFrom);
            sheet.cell(row, 6).value(dataPoint.periodTo);
            sheet.cell(row, 7).value(dataPoint.title);
            sheet.cell(row, 8).value(parseFloat(dataPoint.value) ?? '-');
            sheet.cell(row, 9).value(dataPoint.unitOfMeasure); // ✅ No merge here
            sheet.cell(row, 10).value(dataPoint.emissionFactorName);
            sheet.cell(row, 11).value(dataPoint.efkey);
            sheet.cell(row, 12).value(parseFloat(dataPoint.emissionFactorValue) ?? '-');
            sheet.cell(row, 13).value(parseFloat(dataPoint.emissionFactorCo2Value) ?? '-');
            sheet.cell(row, 14).value(parseFloat(dataPoint.emissionFactorCh4Value) ?? '-');
            sheet.cell(row, 15).value(parseFloat(dataPoint.emissionFactorN2oValue) ?? '-');
            sheet.cell(row, 16).value(dataPoint.methodology || "-");
            sheet.cell(row, 17).value(parseFloat(dataPoint.computedValue) ?? '-');
            sheet.cell(row, 18).value(parseFloat(dataPoint.computedCo2Value) ?? '-');
            sheet.cell(row, 19).value(parseFloat(dataPoint.computedCh4Value) ?? '-');
            sheet.cell(row, 20).value(parseFloat(dataPoint.computedN2oValue) ?? '-');
            sheet.cell(row, 21).value(dataPoint.entity);

            entityComputedValue += Number(dataPoint.computedValue) || 0;

            row++;
          });

          // ✅ Merge Approval Status vertically based on dataPoint count
          if (periodStartRow !== row - 1) {
            sheet.range(`D${periodStartRow}:D${row - 1}`).merged(true).style({
              verticalAlignment: "top",
              horizontalAlignment: "center"
            });

            sheet.range(`E${periodStartRow}:E${row - 1}`).merged(true).style({
              verticalAlignment: "top"
            });

            sheet.range(`F${periodStartRow}:F${row - 1}`).merged(true).style({
              verticalAlignment: "top"
            });
          }
        });

        // ✅ Merge Contributing Entities vertically
        if (entityStartRow !== row - 1) {
          sheet.cell(entityStartRow, 3).value(entity.title).style({
            horizontalAlignment: "center",
            verticalAlignment: "top"
          });
          sheet.range(`C${entityStartRow}:C${row - 1}`).merged(true).style({
            verticalAlignment: "top"
          });

          // ✅ Merge Entity Summary vertically
          sheet.range(`P${entityStartRow}:P${row - 1}`).value(entityComputedValue).merged(true).style({
            horizontalAlignment: "center",
            verticalAlignment: "top"
          });
        }

        totalComputedValue += entityComputedValue;
      });

      // ✅ Merge Indicator, Type, Overall Entity Summary, and Unit of Measure
      if (indicatorStartRow !== row - 1) {
        sheet.cell(indicatorStartRow, 1).value(item.title);
        sheet.cell(indicatorStartRow, 2).value(item.type === 1 ? "Standalone" : "Derived");

        sheet.range(`A${indicatorStartRow}:A${row - 1}`).merged(true).style({
          horizontalAlignment: "center",
          verticalAlignment: "top"
        });

        sheet.range(`B${indicatorStartRow}:B${row - 1}`).merged(true).style({
          horizontalAlignment: "center",
          verticalAlignment: "top"
        });

        // ✅ Merge Unit of Measure vertically
        sheet.range(`O${indicatorStartRow}:O${row - 1}`).value(item.unit).merged(true).style({
          horizontalAlignment: "center",
          verticalAlignment: "top"
        });

        // ✅ Merge Overall Entity Summary vertically
        sheet.range(`Q${indicatorStartRow}:Q${row - 1}`).value(totalComputedValue).merged(true).style({
          horizontalAlignment: "center",
          verticalAlignment: "top"
        });
      }

      row++; // Leave a row for separation
    });

    // ✅ Adjust column widths for better formatting
    const columnWidths = [
      20, 15, 25, 20, 15, 15, 30, 20, 20, 25,
      25, 20, 15, 15, 15, 15, 20, 20, 20, 25
    ];

    headers.forEach((_, i) => {
      sheet.column(i + 1).width(columnWidths[i]);
    });

    // ✅ Create file in browser
    const blob = await workbook.outputAsync();
    const url = URL.createObjectURL(new Blob([blob], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }));

    // ✅ Create link and trigger download
    const link = document.createElement('a');
    link.href = url;
    link.download = (customMetricResponseBk.find(x => x.id === indifilter.indicator)?.title || 'Indicator') + '.xlsx';
    link.click();

    // ✅ Clean up
    URL.revokeObjectURL(url);
  };

  const exportIndicatorReport_grouped_working = async (data) => {
    const workbook = await XlsxPopulate.fromBlankAsync();
    const sheet = workbook.sheet(0);

    let row = 1;

    const headers = [
      "Indicator", "Type", "Contributing Entities", "Approval Status",
      "From", "To", "Contributing Data Points", "Total Quantity",
      "Unit of Measure", "Emission Factor (if applicable)",
      "Emission Factor ID", "Emission Factor Value of Co2e in kgCO2e",
      "Emission Factor Value of C2o in kgCO2e", "Emission Factor Value of Ch4 in kgCO2e",
      "Emission Factor Value of N2o in kgCO2e", "Formula",
      "Computed Value in tCo2e", "Computed Value in tCo2", "Computed Value in tCh4", "Computed Value in tN2o",
      "Unit Of Measure", "Entity Summary", "Enterprise Summary"
    ];
    console.log(data)
    const columnWidths = [
      20, 15, 25, 20, 15, 15, 30, 20, 20, 25,
      25, 20, 20, 20, 20, 20, 25, 20, 20, 20,
      20, 20, 25
    ];

    // Apply header
    headers.forEach((header, i) => {
      sheet.cell(row, i + 1).value(header).style({
        border: true,
        bold: true,
        horizontalAlignment: "center",
        verticalAlignment: "center",
        wrapText: true
      });
      sheet.column(i + 1).width(columnWidths[i]);
    });

    row++;

    data.forEach((item) => {
      const indicatorStartRow = row;
      let totalComputedValue = 0;

      item.contributingEntities.forEach((entity) => {
        const entityStartRow = row;
        let entityComputedValue = 0;

        entity.contributingReportingPeriod.forEach((period) => {
          const periodStartRow = row;

          period.contributingDataPoints.forEach((dataPoint) => {
            sheet.cell(row, 4).value(dataPoint.status); // D
            sheet.cell(row, 5).value(dataPoint.periodFrom); // E
            sheet.cell(row, 6).value(dataPoint.periodTo);   // F
            sheet.cell(row, 7).value(dataPoint.title);      // G
            sheet.cell(row, 8).value(dataPoint.value != null ? parseFloat(dataPoint.value) : '-'); // H
            sheet.cell(row, 9).value(dataPoint.unitOfMeasure); // I
            sheet.cell(row, 10).value(dataPoint.emissionFactorName); // J
            sheet.cell(row, 11).value(dataPoint.efkey); // K
            sheet.cell(row, 12).value(parseFloat(dataPoint.emissionFactorValue) ?? '-'); // L
            sheet.cell(row, 13).value(dataPoint.emissionFactorCo2Value ?? '-'); // M
            sheet.cell(row, 14).value(dataPoint.emissionFactorCh4Value ?? '-'); // N
            sheet.cell(row, 15).value(dataPoint.emissionFactorN2oValue ?? '-'); // O
            sheet.cell(row, 16).value(dataPoint.methodology || "-"); // P

            sheet.cell(row, 17).value(dataPoint.computedValue != null ? parseFloat(dataPoint.computedValue) : '-'); // Q
            sheet.cell(row, 18).value(dataPoint.computedCo2Value != null ? parseFloat(dataPoint.computedCo2Value) : '-'); // R
            sheet.cell(row, 19).value(dataPoint.computedCh4Value != null ? parseFloat(dataPoint.computedCh4Value) : '-'); // S
            sheet.cell(row, 20).value(dataPoint.computedN2oValue != null ? parseFloat(dataPoint.computedN2oValue) : '-'); // T

            sheet.cell(row, 21).value(dataPoint.unitOfMeasure); // U
            sheet.cell(row, 22).value(""); // V - Entity Summary (will be merged and filled later)

            entityComputedValue += dataPoint.computedValue != null ? parseFloat(dataPoint.computedValue) : 0;
            row++;
          });

          // Merge Approval Status, From, To
          if (periodStartRow < row - 1) {
            sheet.range(`D${periodStartRow}:D${row - 1}`).merged(true).style({ verticalAlignment: "top", horizontalAlignment: "center" });
            sheet.range(`E${periodStartRow}:E${row - 1}`).merged(true).style({ verticalAlignment: "top" });
            sheet.range(`F${periodStartRow}:F${row - 1}`).merged(true).style({ verticalAlignment: "top" });
          }
        });

        // Merge Contributing Entities and Entity Summary
        if (entityStartRow < row - 1) {
          sheet.cell(entityStartRow, 3).value(entity.title).style({
            horizontalAlignment: "center",
            verticalAlignment: "top"
          });
          sheet.range(`C${entityStartRow}:C${row - 1}`).merged(true).style({ verticalAlignment: "top" });

          sheet.cell(entityStartRow, 22).value(entityComputedValue);
          sheet.range(`V${entityStartRow}:V${row - 1}`).merged(true).style({
            horizontalAlignment: "center",
            verticalAlignment: "top"
          });
        }

        totalComputedValue += entityComputedValue;
      });

      // Merge Indicator, Type, Unit, and Enterprise Summary
      if (indicatorStartRow < row - 1) {
        sheet.cell(indicatorStartRow, 1).value(item.title); // A
        sheet.cell(indicatorStartRow, 2).value(item.type === 1 ? "Standalone" : "Derived"); // B

        sheet.range(`A${indicatorStartRow}:A${row - 1}`).merged(true).style({ horizontalAlignment: "center", verticalAlignment: "top" });
        sheet.range(`B${indicatorStartRow}:B${row - 1}`).merged(true).style({ horizontalAlignment: "center", verticalAlignment: "top" });

        sheet.cell(indicatorStartRow, 21).value(item.unit); // U
        sheet.range(`U${indicatorStartRow}:U${row - 1}`).merged(true).style({ horizontalAlignment: "center", verticalAlignment: "top" });

        sheet.cell(indicatorStartRow, 23).value(totalComputedValue); // W
        sheet.range(`W${indicatorStartRow}:W${row - 1}`).merged(true).style({ horizontalAlignment: "center", verticalAlignment: "top" });
      }

      row++; // Optional: Spacer row
    });

    const blob = await workbook.outputAsync();
    const url = URL.createObjectURL(new Blob([blob], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }));

    const link = document.createElement('a');
    link.href = url;
    link.download = (customMetricResponseBk.find(x => x.id === indifilter.indicator)?.title || 'Indicator') + '.xlsx';
    link.click();

    URL.revokeObjectURL(url);
  };

  const exportIndicatorReport = async (metricData) => {
    let data = metricData.flatMap(({ contributingEntities, ...x }) => contributingEntities?.flatMap(y => y?.contributingReportingPeriod
      ?.flatMap(z => z?.
        contributingDataPoints?.map(xy => ({ ...xy, ...x }))
      ))
    )
    const workbook = await XlsxPopulate.fromBlankAsync();
    const sheet = workbook.sheet(0);
    const headers = [
      "Indicator", "Type", "Contributing Entities", "Approval Status",
      "From", "To", "Contributing Data Points", "Total Quantity",
      "Unit of Measure", "Emission Factor (if applicable)",
      "Emission Factor ID", "Emission Factor Value of Co2e in kgCO2e",
      "Emission Factor Value of C2o in kgCO2e", "Emission Factor Value of Ch4 in kgCO2e",
      "Emission Factor Value of N2o in kgCO2e", "Formula",
      "Computed Value in tCo2e", "Computed Value in tCo2", "Computed Value in tCh4", "Computed Value in tN2o",
      "Unit Of Measure"
    ];

    const mapItem = item => ({
      "Indicator": item.indicatorTitle,
      "Type": item.type === 1 ? 'Standalone' : 'Derived',
      "Contributing Entities": item.entity,
      "Approval Status": item.status,
      "From": item.periodFrom,
      "To": item.periodTo,
      "Contributing Data Points": item.actualTitle,
      "Total Quantity": item.value,
      "Unit of Measure": item.unitOfMeasure,
      "Emission Factor (if applicable)": item.emissionFactorName,
      "Emission Factor ID": item.efkey,
      "Emission Factor Value of Co2e in kgCO2e": item.emissionFactorValue,
      "Emission Factor Value of C2o in kgCO2e": item.emissionFactorCo2Value,
      "Emission Factor Value of Ch4 in kgCO2e": item.emissionFactorCh4Value,
      "Emission Factor Value of N2o in kgCO2e": item.emissionFactorN2oValue,
      "Formula": item.methodology,
      "Computed Value in tCo2e": item.computedValue,
      "Computed Value in tCo2": item.computedCo2Value,
      "Computed Value in tCh4": item.computedCh4Value,
      "Computed Value in tN2o": item.computedN2oValue,
      "Unit Of Measure": item.unit
    });
    // Write headers
    headers.forEach((header, i) => {
      const cell = sheet.cell(1, i + 1);
      cell.value(header).style({ bold: true, border: true, horizontalAlignment: 'center' });
    });
    console.log(data.map(mapItem),)
    // Write data rows
    data.map(mapItem).forEach((row, rowIndex) => {
      headers.forEach((header, colIndex) => {
        const value = row[header] ?? '';
        const cell = sheet.cell(rowIndex + 2, colIndex + 1);
        cell.value(value).style({ border: true });
      });
    });

    // Auto width
    sheet.usedRange().style("wrapText", true);
    sheet.column("A").width(40); // You can auto-adjust or set specific widths

    // Export
    const buffer = await workbook.outputAsync();

    // Download in browser
    const blob = new Blob([buffer], { type: "application/octet-stream" });
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = (customMetricResponseBk.find(x => x.id === indifilter.indicator)?.title || 'Indicator') + '.xlsx'
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }





  return (
    <Box>
      <div className="col-12">
        <div
          className="col-12 flex align-items-center"
          style={{ padding: "0px 20px" }}
        >
          <p className="text-big-one">

            Welcome {admin_data.information.contactperson} !
          </p>
          <Tag className="ml-3 p-tag-blue" style={{ width: "10rem" }}>

            Enterprise Administrator
          </Tag>
        </div>

        <div className="col-12" style={{ padding: "0px 20px" }}>
          <label className="text-big-one text-navy flex fs-16">

            Data Assurance
          </label>
          <label className="text-small-one text-navy flex fs-16">

            Data integrity and transparency though a comprehensive audit trail
            for internal assurance and external validation.
          </label>
        </div>
      </div>
      <Tabs
        value={activeTab}
        onChange={(e, newValue) => setActiveTab(newValue)}
      >
        <Tab label="Enterprise's Raw Data" value={0} />
        {admin_data.id === 289 && <Tab label="Supplier's Raw Data" onClick={() => { renderSupplierResponse() }} value={1} />}
        {admin_data.id === 289 && <Tab label="Dealer's Raw Data" value={2} />}
        {admin_data.id === 289 && <Tab label="LCA Data" value={3} />}
        <Tab label="Indicator" value={4} onClick={() => { setIndiFilter((prev) => ({ ...prev, year: 0, indicator: 0, section: 0, framework: assFramework.map(i => i.title) })); renderEmissionFactor(); }} />
        <Tab label="Emission Factors" value={5} onClick={() => { renderEmissionFactor() }} />
      </Tabs>
      <Box sx={{ padding: 2 }}>
        {activeTab === 0 && (
          <>
            <p>
              This screen displays the raw data submitted for the selected
              reporting year. Use the filters to choose the year, category, and
              data source to view associated data points, time periods, and
              reporting entities. Within each data point, you can see the
              reported values along with details of who reported, reviewed, and
              approved the data. Clicking on the data point opens its specific
              submission screen, showing the full workflow, attached evidences,
              and additional details. The table will display data only after it
              has been submitted by the Reporter or pulled from a data source.
              Any required data that has not yet been submitted or integrated
              into the platform will not appear here. You can also download this
              report in a CSV format.
            </p>
            <div className="col-12 align-items-end flex justify-content-end">
              <Button
                onClick={() => {
                  exportReport(
                    rawData.filter(x => x.formCategory === filter.source),
                    filter.source === 1 ? rawDcfDataColumns : rawSapDataColumns,
                    "raw_data.csv"
                  );
                }}
                label="Export Report"
                icon="pi pi-download"
                className="p-button-primary mr-3"
              />
            </div>

            <Box display="flex" gap={2} padding={2} alignItems="center">
              <FormControl sx={{ minWidth: '15%' }}>
                <label htmlFor="reporting-period-dropdown">
                  Reporting Year
                </label>
                <Dropdown
                  id="reporting-period-dropdown"
                  value={filter.year}
                  options={[{ label: "All", name: 0 }, ...yearOption]}
                  optionValue="name"
                  optionLabel="label"
                  onChange={(e) => updateDataByFilter('year', e.value)}
                  placeholder="Select Reporting Year"
                />
              </FormControl>

              {/* <FormControl sx={{ minWidth: 200 }} > 
                <label htmlFor="category-dropdown">Topic/Section</label>
                <Dropdown
                  id="category-dropdown"
                  value={filter.section}
                  optionLabel="name"
                  optionValue="id"
                  filter
                  options={sectionlist}
                  onChange={(e) => updateDataByFilter('section', e.value)}
                  placeholder="Select Topic/Section"
                />
              </FormControl> */}

              <FormControl sx={{ minWidth: '20%' }} >
                <label htmlFor="category-dropdown">Framework</label>
                <MultiSelect disabled={filter.source === 2} display="chip" style={{ width: 300 }} value={filter.framework} onChange={(e) => updateDataByFilter('framework', e.value)} options={assFramework} optionLabel="title" optionValue="title"
                  filter={true} placeholder="Select" panelClassName={'hidefilter'} />
              </FormControl>
              <FormControl sx={{ minWidth: '15%' }}>
                <label htmlFor="datasource-dropdown">Data Source</label>
                <Dropdown
                  id="data-source"
                  value={filter.source}
                  filter
                  options={[
                    { label: "DCF", value: 1 },
                    { label: "SAP", value: 2 }
                  ]}
                  onChange={(e) => updateDataByFilter('source', e.value)}

                  placeholder="Select Data Source"
                />
              </FormControl>
              <FormControl sx={{ minWidth: '50%' }}>
                <label htmlFor="dcf-dropdown">Select ID</label>
                <Dropdown
                  id="dcf-dropdown"
                  value={filter.form}
                  filter
                  optionLabel="title"
                  optionValue="id"
                  options={[
                    { title: "All", id: 0 },
                    ...assignedDcf.filter(x => x.formCategory === filter.source),
                  ]}
                  onChange={(e) => updateDataByFilter('form', e.value)}
                  placeholder="Select Id"
                />
              </FormControl>
            </Box>
            <div className="col-12 grid m-0 p-1 align-items-center">
            <div className="col-2 p-md-3">
             Filter by Entity :
             </div>
              <div className="col-1 p-md-3">
                <Dropdown style={{width:'100%'}} value={filter.country} options={locList.country} optionLabel="name" optionValue="id" onChange={(e) => { updateDataByFilter('location',  e.value,'country',) }} placeholder="Select Country" />
              </div>
              {filter.country !== 0 &&
                <div className="col-2 p-md-3">
                  <Dropdown style={{width:'100%'}} value={filter.city} options={locList.city} optionLabel="name" optionValue="id" onChange={(e) => { updateDataByFilter('location', e.value, 'city') }} placeholder="Select Region" />
                </div>}
              {filter.country !== 0 && filter.city !== 0 &&
                <div className="col-3 p-md-3">
                  <Dropdown style={{width:'100%'}} value={filter.site} options={locList.location} optionLabel="name" optionValue="id" onChange={(e) => { updateDataByFilter('location', e.value, 'site') }} placeholder="Select Business Unit" />
                </div>}
            </div>
            {renderDataTable(rawData.filter(x => x.formCategory === filter.source), filter.source === 1 ? rawDcfDataColumns : rawSapDataColumns)}
          </>
        )}
        {activeTab === 123 && dataSource === "sap" && (
          <>
            <p>
              This screen displays the raw data submitted for the selected
              reporting year. Use the filters to choose the year, category, and
              data source to view associated data points, time periods, and
              reporting entities. Within each data point, you can see the
              reported values along with details of who reported, reviewed, and
              approved the data. Clicking on the data point opens its specific
              submission screen, showing the full workflow, attached evidences,
              and additional details. Do note that the data will appear in this
              table only after it has gone through the stipulated internal
              approval process. You can also download this report in a CSV
              format.
            </p>
            <div className="col-12 align-items-end flex justify-content-end">
              <Button
                onClick={() => {
                  exportReport(
                    filteredSapData,
                    rawSapDataColumns,
                    "sap_data.csv"
                  );
                }}
                label="Export Report"
                icon="pi pi-download"
                className="p-button-primary mr-3"
              />
            </div>
            <Box display="flex" gap={2} padding={2} alignItems="center">
              <FormControl sx={{ minWidth: 200 }}>
                <label htmlFor="reporting-period-dropdown">
                  Reporting Year
                </label>
                <Dropdown
                  id="reporting-period-dropdown"
                  value={reportingPeriod}
                  options={[{ label: "All", name: null }, ...yearOption]}
                  optionValue="name"
                  optionLabel="label"
                  onChange={(e) => setReportingPeriod(e.value)}
                  placeholder="Select Reporting Year"
                />
              </FormControl>

              {/* <FormControl sx={{ minWidth: 200 }}>
                <label htmlFor="category-dropdown">Topic/Section</label>
                <Dropdown
                  id="category-dropdown"
                  value={category}
                  filters
                  options={[
                    { label: "All", value: "" },
                    ...Array.from(
                      new Set(
                        assignedDcf?.map((d) =>
                          convertSectionIdToName(d.section)
                        )
                      )
                    ).map((cat) => ({
                      label: cat,
                      value: cat,
                    })),
                  ]}
                  onChange={(e) => setCategory(e.value)}
                  placeholder="Select Topic/Section"
                />
              </FormControl> */}
              <FormControl sx={{ minWidth: 200 }}>
                <label htmlFor="datasource-dropdown">Data Source</label>
                <Dropdown
                  id="data-source"
                  value={dataSource}
                  filter
                  options={[
                    { label: "DCF", value: "dcf" },
                    { label: "SAP", value: "sap" },
                    { label: "SRF", value: "dcf" },
                  ]}
                  onChange={(e) => setDataSource(e.value)}
                  placeholder="Select Data Source"
                />
              </FormControl>
              <FormControl sx={{ minWidth: 550 }}>
                <label htmlFor="dcf-dropdown">Select ID</label>
                <Dropdown
                  id="dcf-dropdown"
                  value={dataSource === "dcf" ? selectedDcf : selectedSap}
                  filter
                  options={[
                    { label: "All", value: "" },
                    ...DataCollectionIdOptions,
                  ]}
                  onChange={(e) => {
                    dataSource === "dcf"
                      ? setSelectedDcf(e.value)
                      : setSelectedSap(e.value);
                  }}
                  placeholder="Select Id"
                />
              </FormControl>
            </Box>
            {renderDataTable(filteredSapData, rawSapDataColumns)}
          </>
        )}
        {activeTab === 1 && (
          <div>
            {!load && <TabsComponent data={attribute} onChange={(e) => {
              setAttributeTableColumn(e.data); setFilteredSrf(supplierSubmission.current.filter(x => x.title === e.title)?.flatMap(item => item.data.map(x => ({ ...x, supplierName: item.supplierName, reportingFrom: item.reportingFrom, reportingTo: item.reportingTo }))))
            }} tabLabelKey={'title'} />}
            <DataTable value={filteredSrf} scrollable loading={load}
              className="custom-datatable"
            >
              <Column
                field="supplierName"
                header="Supplier"

                style={{ minWidth: '150px' }}
              />
              <Column
                field="reportingFrom"
                header="Reporting From"

                style={{ minWidth: '150px' }}
              />
              <Column
                field="reportingTo"
                header="Reporting To"

                style={{ minWidth: '150px' }}
              />


              {attributeColumn.map(i => {
                return <Column field={i.name} header={i.header} />
              })

              }
            </DataTable>

          </div>
        )}
        {activeTab === 3 && (
          <SupplierLCATable />
        )

        }
        {activeTab === 4 && (
          <>
            <p>
              This screen displays indicators for the selected reporting year,
              where "Indicator" refers to computed value
              included in reports or dashboards. Use the filters to select the
              year, category, and indicator to view. Only indicators that have
              completed the approval process are listed here; unapproved data
              points are not visible. For the selected indicator(s), the table
              shows all contributing source data, reporting entities, and, where
              applicable, the associated emission factors. Clicking on a data
              point will open its specific submission screen, providing full
              workflow details, attached evidences, and additional information.
            </p>
            <div className="col-12 align-items-end flex justify-content-end">
              <Button
                disabled={metricsData?.length === 0}
                onClick={() => {
                  exportIndicatorReport(metricsData);
                }}
                label="Export Report"
                icon="pi pi-download"
                className="p-button-primary mr-3"
              />
            </div>
            <Box display="flex" gap={2} padding={2} alignItems="center">
              <FormControl sx={{ minWidth: 200 }}>
                <label htmlFor="reporting-period-dropdown">
                  Reporting Year
                </label>
                <Dropdown
                  id="reporting-period-dropdown"
                  disabled={customMetricResponse?.length === 0}
                  value={indifilter.year}
                  options={[{ label: "All", name: 0 }, ...yearOption]}
                  optionValue="name"
                  optionLabel="label"
                  onChange={(e) => updateDataByIndicatorFilter('year', e.value)}
                  placeholder="Select Reporting Year"
                />
              </FormControl>
              <FormControl sx={{ minWidth: 200 }} >
                <label htmlFor="category-dropdown">Framework</label>
                <MultiSelect disabled={customMetricResponse?.length === 0} display="chip" style={{ width: 300 }} value={indifilter.framework} onChange={(e) => updateDataByIndicatorFilter('framework', e.value)} options={assFramework} optionLabel="title" optionValue="title"
                  filter={true} placeholder="Select" panelClassName={'hidefilter'} />
              </FormControl>

              <FormControl sx={{ minWidth: 200 }}>
                <label htmlFor="datasource-dropdown">Select Indicator</label>
                <Dropdown
                  id="indicator-types"
                  value={indifilter.indicator}
                  disabled={customMetricResponse?.length === 0}
                  filter
                  optionLabel="label"
                  optionValue="value"
                  options={customMetricResponse.map(x => ({ label: x.title, value: x.id }))}
                  onChange={(e) => updateDataByIndicatorFilter('indicator', e.value)}

                  placeholder="Select Type of Indicator"
                />
              </FormControl>
              {/* <FormControl sx={{ minWidth: 550 }}>
                <label htmlFor="dcf-dropdown">
                  Indicator / Metric / Quantitative Disclosure
                </label>
                <Dropdown
                  id="metric"
                  value={metricKpi}
                  filter

                  optionLabel="title"
                  itemTemplate={(op) => { return op.id + ': ' + op.title }}
                  options={customMetricResponse}
                  onChange={(e) => {
                    updateMetricData('indicator', e.value)

                  }}
                  placeholder="Select Indicator / Metric / Quantitative Disclosure"
                />
              </FormControl> */}
            </Box>
            {RenderTable(metricsData)}
            {/* {renderDataTable(metricsData, metricsColumns)} */}
          </>
        )}
        {activeTab === 5 && (
          <div>
            <p>
              This screen provides an overview of the emission factors used to
              calculate CO2e values for various reports and disclosures. Using
              the filters, you can select a specific reporting entity within the
              enterprise to view the corresponding emission factor source,
              category, and sub-categories. Emission values are presented for
              each item within the selected category, based on the unit of
              measurement used to collect data on the Navigos platform. Each
              emission factor in this list has a unique ID that is used in
              computations and can be traced back to its source.
            </p>
            <div className="col-12 align-items-end flex justify-content-end">
              <Button
                onClick={() => {
                  exportReport(
                    efassignment,
                    emissionsColumns,
                    "emissions_data.csv"
                  );
                }}
                label="Export Report"
                icon="pi pi-download"
                className="p-button-primary mr-3"
              />
            </div>
            <DataTable
              scrollable
              showGridlines
              className="custom-datatable"
              loading={load}
              filters={{
                startMonth: { value: null, matchMode: "in" },
                endMonth: { value: null, matchMode: "in" },
                tier1: { value: null, matchMode: "in" },
                tier2: { value: null, matchMode: "in" },
                tier3: { value: null, matchMode: "in" },
                standard: { value: null, matchMode: "in" },
                ghgcategory: { value: null, matchMode: "in" },
                ghgsubcategory: { value: null, matchMode: "in" },
                item: { value: null, matchMode: "in" },
                subCategory1: { value: null, matchMode: "in" },
                subCategory2: { value: null, matchMode: "in" },
                subCategory3: { value: null, matchMode: "in" },
                subCategory4: { value: null, matchMode: "in" }
              }}
              value={efassignment}
              scrollable
            >
              <Column
                header={label1}
                field={"tier1"}
                showFilterMatchModes={false}
                filter
                body={(rowData) => rowData.tier1}
                filterElement={(options) => RowFilterTemplate(options, "tier1")}
              />

              <Column
                header={label3}
                field={"tier2"}
                showFilterMatchModes={false}
                filter
                body={(rowData) => rowData.tier2}
                filterElement={(options) => RowFilterTemplate(options, "tier2")}
              />
              <Column
                header={label3}
                field={"tier3"}
                showFilterMatchModes={false}
                filter
                body={(rowData) => rowData.tier3}
                filterElement={(options) => RowFilterTemplate(options, "tier3")}
              />

              <Column
                header={"EF_ID"}
                bodyStyle={{ width: "150px", minWidth: "150px" }}
                field={"uniqueEfId"}
              />

              <Column
                header={"Standard"}
                field={"standard"}
                showFilterMatchModes={false}
                filter
                filterElement={(options) =>
                  RowFilterTemplate(options, "standard")
                }
              />
              <Column
                header={"Start Month"}
                field={"startMonth"}
                showFilterMatchModes={false}
                filter
                body={(rowData) => rowData.startMonth}
                filterElement={(options) =>
                  RowFilterTemplate(options, "startMonth")
                }
              />
              <Column
                header={"End Month"}
                field={"endMonth"}
                showFilterMatchModes={false}
                filter
                body={(rowData) => rowData.endMonth}
                filterElement={(options) =>
                  RowFilterTemplate(options, "endMonth")
                }
              />
              <Column
                header={"GHG Category"}
                field={"ghgcategory"}
                showFilterMatchModes={false}
                filter
                filterElement={(options) =>
                  RowFilterTemplate(options, "ghgcategory")
                }
              />
              <Column
                header={"GHG SubCategory"}
                field={"ghgsubcategory"}
                showFilterMatchModes={false}
                filter
                filterElement={(options) =>
                  RowFilterTemplate(options, "ghgsubcategory")
                }
              />
              <Column
                header={"Item"}
                field={"item"}
                showFilterMatchModes={false}
                filter
                filterElement={(options) => RowFilterTemplate(options, "item")}
              />
              <Column
                header={"Item Category1"}
                field={"subCategory1"}
                showFilterMatchModes={false}
                filter
                filterElement={(options) =>
                  RowFilterTemplate(options, "subCategory1")
                }
              />
              <Column
                header={"Item Category2"}
                field={"subCategory2"}
                showFilterMatchModes={false}
                filter
                filterElement={(options) =>
                  RowFilterTemplate(options, "subCategory2")
                }
              />
              <Column
                header={"Item Category3"}
                field={"subCategory3"}
                showFilterMatchModes={false}
                filter
                filterElement={(options) =>
                  RowFilterTemplate(options, "subCategory3")
                }
              />
              <Column
                header={"Item Category4"}
                field={"subCategory4"}
                showFilterMatchModes={false}
                filter
                filterElement={(options) =>
                  RowFilterTemplate(options, "subCategory4")
                }
              />
              <Column header={"Co2e in kg"} field={"co2e"} />
            </DataTable>
          </div>
        )}
      </Box>
    </Box>
  );
};

const TabsComponent = ({ data = [], tabLabelKey = 'label', contentKey = 'content', onChange = () => { }, tabStyle = {}, contentStyle = {} }) => {
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    onChange(data[activeTab])
  }, [activeTab])

  return (
    <div>
      {/* Tab Headers */}
      <div style={{
        display: 'flex',
        flexWrap: 'wrap', // Allow tabs to wrap to next line
        borderBottom: '2px solid #ccc',
        marginBottom: '10px',
        gap: '5px' // Add space between tabs
      }}>
        {data.map((item, index) => (
          <div
            key={index}
            onClick={() => { setActiveTab(index); }}
            style={{
              padding: '10px 20px',
              cursor: 'pointer',
              borderBottom: activeTab === index ? '2px solid #007bff' : '2px solid transparent',
              fontWeight: activeTab === index ? 'bold' : 'normal',
              color: activeTab === index ? '#007bff' : '#000',
              borderRadius: '5px',
              backgroundColor: '#f9f9f9',
              ...tabStyle
            }}
          >
            {item[tabLabelKey]}
          </div>
        ))}
      </div>

      {/* Tab Content */}
      <div style={{ padding: '10px', ...contentStyle }}>
        {data[activeTab] && data[activeTab][contentKey]}
      </div>
    </div>
  );
};

