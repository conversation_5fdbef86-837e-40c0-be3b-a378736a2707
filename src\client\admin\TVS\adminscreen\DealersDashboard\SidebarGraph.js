import React, { useState, useRef, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  LabelList,
  ResponsiveContainer,
} from "recharts";
import { Card } from "primereact/card";
import { <PERSON><PERSON> } from "primereact/button";
import { Menu } from "primereact/menu";
import APIServices from "../../../../../service/APIService";
import { API } from "../../../../../constants/api_url";
import { ProgressSpinner } from 'primereact/progressspinner';

const SideBarGraph = () => {
  const [chartData, setChartData] = useState([]);
  const [activeMode, setActiveMode] = useState(true);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const menuRef = useRef(null);
  const tableRef = useRef(null);
  const chartRef = useRef(null);

  useEffect(() => {
    fetchDealerData();
  }, []);

  const fetchDealerData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await APIServices.get(API.dealerResponse);

      if (response.data && Array.isArray(response.data)) {
        // Filter for dealer types 1 and 2 (Authorized Main Dealer and Authorized Dealer)
        const filteredData = response.data.filter(item => item.dealerType === 1 || item.dealerType === 2);

        if (filteredData.length === 0) {
          setError("No dealer data found. Please check your filters or data source.");
          setChartData([]);
          setLoading(false);
          return;
        }

        // Group by zone/region
        const groupedByRegion = {};

        filteredData.forEach(item => {
          const region = item.zone || 'Unknown';

          if (!groupedByRegion[region]) {
            groupedByRegion[region] = {
              region,
              totalDealers: 0,
              audited: 0
            };
          }

          groupedByRegion[region].totalDealers++;

          // Check if this dealer has been calibrated (has a completed submission)
          if (item.dealerAuditorChecklistSubmission &&
              [1, 2].includes(item.dealerAuditorChecklistSubmission.type)) {
            groupedByRegion[region].audited++;
          }
        });

        // Convert to array and calculate percentages
        const formattedData = Object.values(groupedByRegion).map(({ region, totalDealers, audited }) => ({
          region,
          totalDealers,
          audited,
          auditedPercent: totalDealers > 0 ? ((audited / totalDealers) * 100).toFixed(2) : '0.00',
          totalDealersPercent: 100
        }));

        // Sort by region name
        formattedData.sort((a, b) => a.region.localeCompare(b.region));

        setChartData(formattedData);
      } else {
        setError("Invalid data format received from the server.");
        setChartData([]);
      }
      setLoading(false);
    } catch (error) {
      console.error("Error fetching dealer data:", error);
      setError("Failed to load dealer data. Please try again later.");
      setChartData([]);
      setLoading(false);
    }
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div
          style={{
            backgroundColor: "#fff",
            border: "1px solid #ccc",
            borderRadius: "8px",
            padding: "10px",
            fontSize: "14px",
            fontFamily: "Lato",
            lineHeight: "1.5",
          }}
        >
          <p style={{ margin: 0, fontWeight: "bold" }}>{label}</p>
          {payload.map((entry) => (
            <p key={entry.name} style={{ margin: 0, color: "black" }}>{`${entry.name
              }:${entry.value}(${entry.payload[`${entry.dataKey}Percent`]}%)`}</p>
          ))}
        </div>
      );
    }

    return null;
  };

  // Function to export data to Excel
  const exportToExcel = () => {
    if (!chartData || chartData.length === 0) return;

    import('xlsx').then(XLSX => {
      // Create worksheet
      const worksheet = XLSX.utils.json_to_sheet(chartData.map(item => ({
        'Region': item.region,
        'Total Dealers': item.totalDealers,
        'Calibrated Dealers': item.audited,
        'Percentage (%)': item.auditedPercent
      })));

      // Set column widths
      const wscols = [
        { wch: 20 }, // Region
        { wch: 15 }, // Total Dealers
        { wch: 20 }, // Calibrated Dealers
        { wch: 15 }  // Percentage
      ];
      worksheet['!cols'] = wscols;

      // Create workbook
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Dealers Calibration');

      // Generate Excel file
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

      // Save file
      import('file-saver').then(FileSaver => {
        FileSaver.saveAs(data, 'Dealers_MSI_Calibration_Data.xlsx');
      });
    });
  };

  const panelItems = [
    {
      items: [
        {
          label: "Export as Excel",
          icon: "pi pi-file-excel",
          command: exportToExcel,
        },
        {
          label: "Export as PDF",
          icon: "pi pi-file-pdf",
          command: () => {
            // This would require additional libraries like jsPDF
            alert('PDF export functionality will be implemented in a future update.');
          },
        },
        {
          label: "Print",
          icon: "pi pi-print",
          command: () => {
            window.print();
          },
        },
      ],
    },
  ];

  const CustomLegend = (props) => {
    const { payload } = props;
    return (
      <ul
        style={{
          display: "flex",
          listStyleType: "none",
          justifyContent: "center",
          padding: 0,
        }}
      >
        {payload.map((entry, index) => (
          <li
            key={`item-${index}`}
            style={{
              color: entry.color,
              marginRight: "5px",
            }}
          >
            <span
              style={{
                color: entry.color,
                backgroundColor: entry.color,
                marginRight: 4,
                fontSize: "20px",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                display: "inline-block",
              }}
            ></span>
            <span style={{ color: "#555", fontSize: "14px" }}>
              {entry.value}
            </span>
          </li>
        ))}
      </ul>
    );
  };

  return (
    <Card style={{ width: "100%" }}>
      <div style={{ display: "flex", justifyContent: "space-between" }}>
        <h3 style={{ fontSize: "18px", margin: "25px" }}>
         Total No of Dealers vs Dealers Calibrated under MSI
        </h3>
        <div
          style={{
            margin: "18px 10px 18px 10px",
            display: "flex",
          }}
        >
          <div
            className="buttons"
            style={{
              background: "#F0F2F4",
              borderRadius: "3px",
              width: "4.5rem",
              marginLeft: "10px",
              height: "30px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Button
              style={{
                background: `${!activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
                marginRight: "4px",
              }}
              onClick={() => {
                setActiveMode(false);
              }}
            >
              <i className="pi pi-table fs-19" />
            </Button>
            <Button
              style={{
                background: `${activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
              }}
              onClick={() => {
                setActiveMode(true);
              }}
            >
              <i className="pi pi-chart-bar fs-19" />
            </Button>
          </div>
          <Button
              style={{
                color: "black",
                height: "30px",
                marginLeft: "3px",
                background: "#F0F2F4",
                border: "0px",
                padding: "6px",
              }}
              onClick={fetchDealerData}
              tooltip="Refresh Data"
              tooltipOptions={{ position: 'bottom' }}
            >
              <i className="pi pi-refresh fs-19" />
            </Button>
          <div ref={menuRef}>
            <Button
              style={{
                color: "black",
                height: "30px",
                marginLeft: "3px",
                background: "#F0F2F4",
                border: "0px",
                padding: "6px",
                position: "relative",
              }}
              onClick={() => {
                setDropdownOpen(!dropdownOpen);
              }}
            >
              <i className="pi pi-angle-down fs-19" />
            </Button>
            {dropdownOpen && (
              <Menu
                model={panelItems}
                style={{
                  position: "absolute",
                  right: 45,
                  zIndex: "1",
                  padding: 0,
                }}
              ></Menu>
            )}
          </div>
        </div>
      </div>

      {loading ? (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
          <ProgressSpinner style={{ width: '50px', height: '50px' }} strokeWidth="4" />
        </div>
      ) : error ? (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
          <div style={{ color: 'red', textAlign: 'center', padding: '20px', backgroundColor: '#fff3f3', borderRadius: '5px', border: '1px solid #ffcccb' }}>
            <i className="pi pi-exclamation-triangle" style={{ fontSize: '2rem', marginBottom: '10px' }}></i>
            <div>{error}</div>
            <Button
              label="Retry"
              icon="pi pi-refresh"
              className="p-button-sm p-button-outlined p-button-danger"
              onClick={fetchDealerData}
              style={{ marginTop: '15px' }}
            />
          </div>
        </div>
      ) : (
        <div style={{ padding: '0 25px', marginBottom: '20px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px' }}>
            <div style={{ flex: 1, padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '5px', marginRight: '10px', textAlign: 'center' }}>
              <div style={{ fontSize: '14px', color: '#666' }}>Total Dealers</div>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#0000cc' }}>
                {chartData.reduce((sum, item) => sum + item.totalDealers, 0)}
              </div>
            </div>
            <div style={{ flex: 1, padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '5px', marginRight: '10px', textAlign: 'center' }}>
              <div style={{ fontSize: '14px', color: '#666' }}>Calibrated Dealers</div>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#006600' }}>
                {chartData.reduce((sum, item) => sum + item.audited, 0)}
              </div>
            </div>
            <div style={{ flex: 1, padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '5px', textAlign: 'center' }}>
              <div style={{ fontSize: '14px', color: '#666' }}>Calibration Percentage</div>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ff6600' }}>
                {chartData.length > 0 ?
                  ((chartData.reduce((sum, item) => sum + item.audited, 0) /
                    chartData.reduce((sum, item) => sum + item.totalDealers, 0)) * 100).toFixed(2) :
                  '0.00'}%
              </div>
            </div>
          </div>
        </div>
      )}

      {!loading && !error && activeMode ? (
        <ResponsiveContainer width="100%" height={400}>
          <BarChart barSize={60} data={chartData}>
            <YAxis type="number" />
            <XAxis dataKey="region" type="category"></XAxis>
            <Tooltip content={CustomTooltip} />
            <Legend content={CustomLegend} />
            <Bar dataKey="totalDealers" fill="#0000cc" name="Total No of Dealers">
              <LabelList
                dataKey="totalDealers"
                position="top"
                style={{ fontSize: "12px", fill: "black" }}
              />
            </Bar>
            <Bar
              dataKey="audited"
              fill="#006600"
              name="Dealers Calibrated under MSI"
            >
              <LabelList
                dataKey="auditedPercent"
                position="top"
                formatter={(value) => `${value}%`}
                style={{ fontSize: "12px", fill: "black" }}
              />
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      ) : !activeMode && (
        <div className="p-datatable p-component" style={{ height: '400px', overflowY: 'auto' }}>
          <table className="p-datatable-table">
            <thead className="p-datatable-thead">
              <tr>
                <th style={{ width: '30%', textAlign: 'left', padding: '1rem' }}>Region</th>
                <th style={{ width: '25%', textAlign: 'center', padding: '1rem' }}>Total Dealers</th>
                <th style={{ width: '25%', textAlign: 'center', padding: '1rem' }}>Calibrated Dealers</th>
                <th style={{ width: '20%', textAlign: 'center', padding: '1rem' }}>Percentage</th>
              </tr>
            </thead>
            <tbody className="p-datatable-tbody">
              {chartData.map((item, index) => (
                <tr key={index} className={index % 2 === 0 ? 'p-datatable-even' : 'p-datatable-odd'}>
                  <td style={{ padding: '0.5rem 1rem' }}>{item.region}</td>
                  <td style={{ textAlign: 'center', padding: '0.5rem 1rem' }}>{item.totalDealers}</td>
                  <td style={{ textAlign: 'center', padding: '0.5rem 1rem' }}>{item.audited}</td>
                  <td style={{ textAlign: 'center', padding: '0.5rem 1rem' }}>{item.auditedPercent}%</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </Card>
  );
};

export default SideBarGraph;
